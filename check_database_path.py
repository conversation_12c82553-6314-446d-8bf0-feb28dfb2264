#!/usr/bin/env python3
"""
检查应用程序实际使用的数据库路径
"""
from app import create_app
from models import db
import os
import sqlite3

def check_database_path():
    """检查应用程序实际使用的数据库路径"""
    print('=== 数据库路径检查 ===\n')
    
    # 1. 检查配置中的数据库URI
    print('1. 检查配置中的数据库URI:')
    
    app = create_app()
    with app.app_context():
        db_uri = app.config['SQLALCHEMY_DATABASE_URI']
        print(f'   配置的数据库URI: {db_uri}')
        
        # 解析SQLite数据库路径
        if db_uri.startswith('sqlite:///'):
            db_path = db_uri.replace('sqlite:///', '')
            print(f'   解析的数据库路径: {db_path}')
            
            # 检查是否是相对路径
            if not os.path.isabs(db_path):
                print(f'   这是相对路径，相对于应用程序工作目录')
                abs_path = os.path.abspath(db_path)
                print(f'   绝对路径: {abs_path}')
            else:
                abs_path = db_path
                print(f'   这是绝对路径')
        else:
            print(f'   这不是SQLite数据库')
            return
    
    # 2. 检查当前工作目录
    print(f'\n2. 检查当前工作目录:')
    
    current_dir = os.getcwd()
    print(f'   当前工作目录: {current_dir}')
    
    # 3. 检查Flask实例文件夹
    print(f'\n3. 检查Flask实例文件夹:')
    
    with app.app_context():
        instance_path = app.instance_path
        print(f'   Flask实例路径: {instance_path}')
        
        # 检查实例目录是否存在
        if os.path.exists(instance_path):
            print(f'   ✅ 实例目录存在')
            
            # 列出实例目录中的文件
            instance_files = os.listdir(instance_path)
            print(f'   实例目录文件: {instance_files}')
            
            # 检查实例目录中是否有数据库文件
            db_files = [f for f in instance_files if f.endswith('.db')]
            if db_files:
                print(f'   实例目录中的数据库文件: {db_files}')
            else:
                print(f'   实例目录中没有数据库文件')
        else:
            print(f'   ❌ 实例目录不存在')
    
    # 4. 检查根目录中的数据库文件
    print(f'\n4. 检查根目录中的数据库文件:')
    
    root_files = os.listdir('.')
    root_db_files = [f for f in root_files if f.endswith('.db')]
    
    if root_db_files:
        print(f'   根目录中的数据库文件: {root_db_files}')
        
        for db_file in root_db_files:
            file_size = os.path.getsize(db_file) / (1024 * 1024)  # MB
            print(f'     - {db_file}: {file_size:.2f} MB')
    else:
        print(f'   根目录中没有数据库文件')
    
    # 5. 检查实际连接的数据库
    print(f'\n5. 检查实际连接的数据库:')
    
    with app.app_context():
        try:
            # 获取数据库连接信息
            engine = db.engine
            print(f'   数据库引擎: {engine}')
            print(f'   数据库URL: {engine.url}')
            
            # 对于SQLite，获取实际文件路径
            if str(engine.url).startswith('sqlite:///'):
                actual_db_path = str(engine.url).replace('sqlite:///', '')
                if not os.path.isabs(actual_db_path):
                    actual_db_path = os.path.abspath(actual_db_path)
                
                print(f'   实际数据库文件: {actual_db_path}')
                
                if os.path.exists(actual_db_path):
                    file_size = os.path.getsize(actual_db_path) / (1024 * 1024)  # MB
                    print(f'   文件大小: {file_size:.2f} MB')
                    print(f'   ✅ 数据库文件存在')
                    
                    # 检查数据库内容
                    try:
                        conn = sqlite3.connect(actual_db_path)
                        cursor = conn.cursor()
                        
                        # 获取表列表
                        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                        tables = [row[0] for row in cursor.fetchall()]
                        print(f'   数据库表数量: {len(tables)}')
                        
                        # 检查关键表的记录数
                        if 'xui_panels' in tables:
                            cursor.execute("SELECT COUNT(*) FROM xui_panels")
                            panel_count = cursor.fetchone()[0]
                            print(f'   面板数量: {panel_count}')
                        
                        if 'subscriptions' in tables:
                            cursor.execute("SELECT COUNT(*) FROM subscriptions")
                            sub_count = cursor.fetchone()[0]
                            print(f'   订阅数量: {sub_count}')
                        
                        conn.close()
                        
                    except Exception as e:
                        print(f'   检查数据库内容失败: {e}')
                        
                else:
                    print(f'   ❌ 数据库文件不存在')
                    
        except Exception as e:
            print(f'   检查数据库连接失败: {e}')
    
    # 6. 分析为什么使用instance目录
    print(f'\n6. 分析为什么使用instance目录:')
    
    print(f'   Flask应用程序的数据库路径解析规则:')
    print(f'   1. 如果数据库URI是相对路径（如 "sqlite:///node_sales.db"）')
    print(f'   2. Flask会相对于应用程序的工作目录来解析路径')
    print(f'   3. 但是SQLAlchemy可能会使用Flask的实例目录作为基准')
    print(f'   ')
    print(f'   可能的原因:')
    print(f'   - Flask实例目录是专门用于存储应用程序数据的地方')
    print(f'   - 这是Flask的最佳实践，将数据文件与代码文件分离')
    print(f'   - 实例目录在部署时更容易管理和备份')
    
    # 7. 建议解决方案
    print(f'\n7. 建议解决方案:')
    
    print(f'   如果希望使用根目录下的数据库文件:')
    print(f'   ')
    print(f'   方案1: 修改配置文件中的数据库URI')
    print(f'   将 SQLALCHEMY_DATABASE_URI 改为绝对路径:')
    print(f'   SQLALCHEMY_DATABASE_URI = f"sqlite:///{os.path.abspath("node_sales.db")}"')
    print(f'   ')
    print(f'   方案2: 移动数据库文件到实例目录')
    print(f'   将根目录下的 node_sales.db 移动到实例目录')
    print(f'   ')
    print(f'   方案3: 创建符号链接（Linux/Mac）')
    print(f'   在实例目录中创建指向根目录数据库文件的符号链接')
    
    # 8. 检查环境变量
    print(f'\n8. 检查环境变量:')
    
    database_url = os.environ.get('DATABASE_URL')
    if database_url:
        print(f'   DATABASE_URL 环境变量: {database_url}')
    else:
        print(f'   DATABASE_URL 环境变量: 未设置')
    
    flask_env = os.environ.get('FLASK_ENV')
    if flask_env:
        print(f'   FLASK_ENV 环境变量: {flask_env}')
    else:
        print(f'   FLASK_ENV 环境变量: 未设置')
    
    print(f'\n=== 数据库路径检查完成 ===')

if __name__ == '__main__':
    check_database_path()
