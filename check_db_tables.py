#!/usr/bin/env python3
"""
检查数据库表结构
"""
import sqlite3

def main():
    conn = sqlite3.connect('instance/node_sales.db')
    cursor = conn.cursor()

    # 查看所有表
    cursor.execute('SELECT name FROM sqlite_master WHERE type="table"')
    tables = cursor.fetchall()
    print('数据库中的表:')
    for table in tables:
        print(f'  - {table[0]}')

    # 如果有products表，查看其结构
    if any('products' in table[0] for table in tables):
        print('\nproducts表结构:')
        cursor.execute('PRAGMA table_info(products)')
        columns = cursor.fetchall()
        for column in columns:
            print(f'  {column[1]} ({column[2]})')
        
        # 查看products表数据
        print('\nproducts表数据:')
        cursor.execute('SELECT * FROM products')
        products = cursor.fetchall()
        for product in products:
            print(f'  {product}')

    conn.close()

if __name__ == '__main__':
    main()
