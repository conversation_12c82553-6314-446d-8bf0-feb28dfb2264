#!/usr/bin/env python3
"""
更新产品分组关联
"""
from app import create_app
from models import db, Product, XUIPanelGroup

def main():
    app = create_app()
    with app.app_context():
        print('=== 更新产品分组关联 ===\n')
        
        # 获取产品和分组
        product = Product.query.first()
        group = XUIPanelGroup.query.first()
        
        if product and group:
            print(f'产品: {product.name} (ID: {product.id})')
            print(f'当前分组ID: {product.target_group_id}')
            print(f'分组: {group.name} (ID: {group.id})')
            
            # 关联产品到分组
            product.target_group_id = group.id
            db.session.commit()
            
            print(f'\n✓ 产品已关联到分组')
            print(f'产品 {product.name} -> 分组 {group.name}')
            
            # 验证更新
            updated_product = Product.query.get(product.id)
            print(f'验证: 产品分组ID = {updated_product.target_group_id}')
        else:
            print('未找到产品或分组')

if __name__ == '__main__':
    main()
