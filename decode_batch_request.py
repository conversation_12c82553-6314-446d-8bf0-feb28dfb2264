#!/usr/bin/env python3
"""
解码批量添加客户端的请求数据
"""
import urllib.parse
import json

def main():
    # 解码URL编码的数据
    encoded_data = 'id=1&settings=%7B%22clients%22%3A%20%5B%7B%0A%20%20%22id%22%3A%20%224942e56c-9e81-40cd-8281-158d5bbdb51c%22%2C%0A%20%20%22flow%22%3A%20%22%22%2C%0A%20%20%22email%22%3A%20%22x14v683l%22%2C%0A%20%20%22limitIp%22%3A%200%2C%0A%20%20%22totalGB%22%3A%200%2C%0A%20%20%22expiryTime%22%3A%200%2C%0A%20%20%22enable%22%3A%20true%2C%0A%20%20%22tgId%22%3A%20%22%22%2C%0A%20%20%22subId%22%3A%20%22otspotgh3jh3l85v%22%2C%0A%20%20%22comment%22%3A%20%22%22%2C%0A%20%20%22reset%22%3A%200%2C%0A%20%20%22security%22%3A%20%22auto%22%0A%7D%2C%7B%0A%20%20%22id%22%3A%20%22a4db803c-3c1f-4e33-90be-0ed496379458%22%2C%0A%20%20%22flow%22%3A%20%22%22%2C%0A%20%20%22email%22%3A%20%22l5ygodma%22%2C%0A%20%20%22limitIp%22%3A%200%2C%0A%20%20%22totalGB%22%3A%200%2C%0A%20%20%22expiryTime%22%3A%200%2C%0A%20%20%22enable%22%3A%20true%2C%0A%20%20%22tgId%22%3A%20%22%22%2C%0A%20%20%22subId%22%3A%20%229yrvvs2hq3mhid1e%22%2C%0A%20%20%22comment%22%3A%20%22%22%2C%0A%20%20%22reset%22%3A%200%2C%0A%20%20%22security%22%3A%20%22auto%22%0A%7D%5D%7D'

    decoded_data = urllib.parse.unquote(encoded_data)
    print('解码后的数据:')
    print(decoded_data)

    # 解析参数
    params = urllib.parse.parse_qs(decoded_data)
    print('\n解析后的参数:')
    print(f'id: {params["id"][0]}')

    settings_json = params['settings'][0]
    print(f'\nsettings JSON:')
    print(settings_json)

    # 解析JSON
    settings = json.loads(settings_json)
    print(f'\n客户端数量: {len(settings["clients"])}')
    print('\n客户端详情:')
    for i, client in enumerate(settings['clients'], 1):
        print(f'  客户端 {i}:')
        print(f'    ID: {client["id"]}')
        print(f'    Email: {client["email"]}')
        print(f'    SubID: {client["subId"]}')
        print(f'    TotalGB: {client["totalGB"]}')
        print(f'    ExpiryTime: {client["expiryTime"]}')
        print()

if __name__ == '__main__':
    main()
