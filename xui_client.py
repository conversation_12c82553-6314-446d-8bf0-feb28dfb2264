"""
X-UI API 客户端
"""
import requests
import json
import uuid
import logging
from typing import Dict, List, Optional, Tuple
from config import Config
from services.config_service import config_service

logger = logging.getLogger(__name__)

class XUIClient:
    """X-UI面板API客户端"""
    
    def __init__(self, base_url: str = None, username: str = None, password: str = None, path_prefix: str = None):
        # 获取默认面板配置
        default_panels = config_service.get_xui_panels()
        default_panel = list(default_panels.values())[0] if default_panels else {}

        self.base_url = base_url or default_panel.get('base_url', 'http://localhost:54321')
        self.path_prefix = path_prefix or default_panel.get('path_prefix', '/')
        self.username = username or default_panel.get('username', 'admin')
        self.password = password or default_panel.get('password', 'admin')
        self.session = requests.Session()
        self.session.timeout = Config.REQUEST_TIMEOUT
        self.is_logged_in = False
        
    def _get_url(self, path: str) -> str:
        """构建完整的URL"""
        return f"{self.base_url}{self.path_prefix}{path}"
    
    def login(self) -> bool:
        """登录到X-UI面板"""
        try:
            login_url = self._get_url(Config.XUI_LOGIN_PATH)
            
            # 准备登录数据
            login_data = {
                'username': self.username,
                'password': self.password
            }
            
            # 设置请求头
            headers = {
                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json, text/plain, */*'
            }
            
            response = self.session.post(
                login_url,
                data=login_data,
                headers=headers,
                allow_redirects=False
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success', False):
                    self.is_logged_in = True
                    logger.info("成功登录到X-UI面板")
                    return True
                else:
                    logger.error(f"登录失败: {result.get('msg', '未知错误')}")
                    return False
            else:
                logger.error(f"登录请求失败，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"登录过程中发生错误: {str(e)}")
            return False
    
    def _ensure_logged_in(self) -> bool:
        """确保已登录"""
        if not self.is_logged_in:
            return self.login()
        return True
    
    def get_inbounds(self) -> Optional[List[Dict]]:
        """获取所有入站规则 - 使用POST /panel/inbound/list API接口"""
        if not self._ensure_logged_in():
            return None

        try:
            # 使用正确的API端点获取入站规则
            api_url = self._get_url(Config.XUI_INBOUNDS_API_PATH)

            headers = {
                'Accept': 'application/json, text/plain, */*',
                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                'X-Requested-With': 'XMLHttpRequest',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Cache-Control': 'max-age=0',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            }

            # 使用POST请求获取入站规则（根据用户提供的正确端点信息）
            response = self.session.post(api_url, headers=headers)

            logger.debug(f"API获取入站规则响应状态码: {response.status_code}")
            logger.debug(f"API响应内容: {response.text[:200]}...")

            if response.status_code == 200:
                # 检查响应内容类型
                content_type = response.headers.get('content-type', '').lower()

                if 'application/json' in content_type:
                    # 如果是JSON响应，直接解析
                    try:
                        result = response.json()
                        if isinstance(result, list):
                            # 直接返回入站规则数组
                            logger.info(f"通过API成功获取 {len(result)} 个入站规则")
                            return result
                        elif isinstance(result, dict) and result.get('success', False):
                            # 标准API响应格式
                            inbounds = result.get('obj', [])
                            logger.info(f"通过API成功获取 {len(inbounds)} 个入站规则")
                            return inbounds
                        else:
                            logger.warning(f"API返回格式异常: {result}")
                    except json.JSONDecodeError as e:
                        logger.warning(f"API响应JSON解析失败: {e}")

                elif 'text/html' in content_type:
                    # 如果返回HTML，说明可能需要从HTML中解析数据
                    logger.info("API返回HTML内容，尝试从HTML页面解析入站规则...")
                    return self._parse_inbounds_from_response(response.text)

                else:
                    logger.warning(f"未知的响应内容类型: {content_type}")

            else:
                logger.warning(f"API请求失败，状态码: {response.status_code}")

            # 如果API方式失败，尝试从HTML页面解析
            logger.info("API方式失败，尝试从HTML页面解析入站规则...")
            return self._parse_inbounds_from_html()

        except Exception as e:
            logger.error(f"获取入站规则时发生错误: {str(e)}")
            return None

    def _parse_inbounds_from_response(self, html_content: str) -> Optional[List[Dict]]:
        """从HTML响应内容解析入站规则"""
        try:
            logger.debug(f"HTML内容长度: {len(html_content)}")

            # 使用更精确的正则表达式模式
            import re

            # 模式1: 查找 inbounds 数组定义
            patterns = [
                # JavaScript变量定义
                r'(?:var|let|const)\s+inbounds\s*=\s*(\[[\s\S]*?\]);',
                # 对象属性
                r'inbounds\s*:\s*(\[[\s\S]*?\])',
                # JSON数据
                r'"inbounds"\s*:\s*(\[[\s\S]*?\])',
                # 直接数组赋值
                r'inbounds\s*=\s*(\[[\s\S]*?\]);',
                # 模板中的数据
                r'data-inbounds\s*=\s*["\'](\[[\s\S]*?\])["\']',
            ]

            for i, pattern in enumerate(patterns):
                logger.debug(f"尝试模式 {i+1}: {pattern}")
                matches = re.finditer(pattern, html_content, re.MULTILINE | re.DOTALL)

                for match in matches:
                    try:
                        inbounds_json = match.group(1)
                        logger.debug(f"找到匹配内容: {inbounds_json[:100]}...")

                        # 清理JSON字符串
                        inbounds_json = inbounds_json.strip()
                        if inbounds_json.endswith(','):
                            inbounds_json = inbounds_json[:-1]

                        inbounds = json.loads(inbounds_json)
                        if isinstance(inbounds, list) and len(inbounds) > 0:
                            logger.info(f"使用模式 {i+1} 成功解析到 {len(inbounds)} 个入站规则")
                            return inbounds
                    except json.JSONDecodeError as e:
                        logger.debug(f"模式 {i+1} JSON解析失败: {e}")
                        continue
                    except Exception as e:
                        logger.debug(f"模式 {i+1} 处理失败: {e}")
                        continue

            # 如果所有模式都失败，尝试查找任何包含端口信息的JSON数组
            port_pattern = r'(\[[\s\S]*?"port"\s*:\s*\d+[\s\S]*?\])'
            matches = re.finditer(port_pattern, html_content, re.MULTILINE | re.DOTALL)

            for match in matches:
                try:
                    potential_json = match.group(1)
                    logger.debug(f"找到包含端口的JSON: {potential_json[:100]}...")

                    inbounds = json.loads(potential_json)
                    if isinstance(inbounds, list) and len(inbounds) > 0:
                        # 验证是否包含入站规则的必要字段
                        first_item = inbounds[0]
                        if isinstance(first_item, dict) and 'port' in first_item:
                            logger.info(f"通过端口模式成功解析到 {len(inbounds)} 个入站规则")
                            return inbounds
                except:
                    continue

            logger.warning("无法从HTML内容中提取入站规则数据")
            return None

        except Exception as e:
            logger.error(f"从HTML内容解析入站规则时发生错误: {str(e)}")
            return None

    def _parse_inbounds_from_html(self) -> Optional[List[Dict]]:
        """从HTML页面解析入站规则"""
        try:
            url = self._get_url(Config.XUI_INBOUNDS_PATH)

            headers = {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Cache-Control': 'max-age=0',
                'Upgrade-Insecure-Requests': '1',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            }

            response = self.session.get(url, headers=headers)

            if response.status_code != 200:
                logger.error(f"HTML页面请求失败，状态码: {response.status_code}")
                return None

            html_content = response.text
            result = self._parse_inbounds_from_response(html_content)

            if result is None:
                # 保存HTML内容用于调试
                with open('debug_inbounds.html', 'w', encoding='utf-8') as f:
                    f.write(html_content)
                logger.info("HTML内容已保存到 debug_inbounds.html 用于调试")
                logger.debug(f"HTML内容片段: {html_content[:1000]}...")

            return result

        except Exception as e:
            logger.error(f"从HTML解析入站规则时发生错误: {str(e)}")
            return None
    
    def add_client_to_inbound(self, inbound_id: int, client_email: str,
                             client_id: str = None, traffic_limit_gb: int = 100,
                             expiry_days: int = 30) -> Tuple[bool, Optional[Dict]]:
        """向指定入站规则添加客户端"""
        if not self._ensure_logged_in():
            return False, None

        try:
            # 如果没有提供client_id，生成一个新的UUID
            if not client_id:
                client_id = str(uuid.uuid4())

            # 计算流量限制（转换为字节，0表示无限制）
            traffic_limit_bytes = traffic_limit_gb * 1024 * 1024 * 1024 if traffic_limit_gb > 0 else 0

            # 计算过期时间（毫秒时间戳，0表示永不过期）
            import time
            expiry_time = int((time.time() + expiry_days * 24 * 3600) * 1000) if expiry_days > 0 else 0

            # 生成subId
            import random
            import string
            sub_id = ''.join(random.choices(string.ascii_lowercase + string.digits, k=20))

            # 创建客户端配置 - 根据3x-ui v2.6.0的格式
            client_config = {
                "id": client_id,
                "flow": "",
                "email": client_email,
                "limitIp": 0,
                "totalGB": traffic_limit_bytes,
                "expiryTime": expiry_time,
                "enable": True,
                "tgId": "",
                "subId": sub_id,
                "comment": "",
                "reset": 0
            }

            # 准备请求数据 - 修正数据类型
            request_data = {
                "id": inbound_id,  # 使用整数类型，不是字符串
                "settings": json.dumps({
                    "clients": [client_config]
                })  # settings需要是JSON字符串
            }

            # 使用正确的添加客户端API
            url = self._get_url(Config.XUI_ADD_CLIENT_PATH)

            # 设置请求头
            headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json, text/plain, */*'
            }

            response = self.session.post(url, json=request_data, headers=headers)

            if response.status_code == 200:
                # 检查响应是否为空字符串（已知的v2.6.0 bug）
                if response.text.strip() == "":
                    logger.warning("收到空响应，这可能是3x-ui v2.6.0的已知问题，但客户端可能已成功添加")
                    return True, client_config

                try:
                    result = response.json()
                    if result.get('success', False):
                        logger.info(f"成功添加客户端: {client_email}")
                        return True, client_config
                    else:
                        logger.error(f"添加客户端失败: {result.get('msg', '未知错误')}")
                        return False, None
                except json.JSONDecodeError:
                    logger.warning("响应不是有效的JSON格式，但状态码为200，假设添加成功")
                    return True, client_config
            else:
                logger.error(f"添加客户端请求失败，状态码: {response.status_code}")
                logger.error(f"响应内容: {response.text}")
                return False, None

        except Exception as e:
            logger.error(f"添加客户端时发生错误: {str(e)}")
            return False, None
    
    def get_client_traffic(self, client_email: str) -> Optional[Dict]:
        """获取指定客户端的流量使用情况 - 使用 /panel/inbound/list API"""
        if not self._ensure_logged_in():
            return None

        try:
            # 获取所有入站规则和客户端统计信息
            inbounds = self.get_inbounds()
            if not inbounds:
                logger.error("无法获取入站规则列表")
                return None

            # 在所有入站规则的客户端统计中查找指定邮箱
            for inbound in inbounds:
                client_stats = inbound.get('clientStats', [])
                for client_stat in client_stats:
                    if client_stat.get('email') == client_email:
                        # 添加入站规则信息到客户端统计中
                        client_stat['inbound_id'] = inbound.get('id')
                        client_stat['inbound_port'] = inbound.get('port')
                        client_stat['inbound_protocol'] = inbound.get('protocol')
                        client_stat['inbound_remark'] = inbound.get('remark')
                        logger.info(f"找到客户端 {client_email} 的流量统计")
                        return client_stat

            logger.warning(f"未找到客户端 {client_email} 的流量统计")
            return None

        except Exception as e:
            logger.error(f"获取客户端流量信息时发生错误: {str(e)}")
            return None

    def client_exists_by_email(self, client_email: str) -> bool:
        """检查指定邮箱的客户端是否已存在

        Args:
            client_email: 客户端邮箱

        Returns:
            bool: 客户端是否存在
        """
        if not self._ensure_logged_in():
            return False

        try:
            # 获取所有入站规则
            inbounds = self.get_inbounds()
            if not inbounds:
                return False

            # 在所有入站规则中查找指定邮箱的客户端
            for inbound in inbounds:
                settings_str = inbound.get('settings', '{}')
                try:
                    settings = json.loads(settings_str) if isinstance(settings_str, str) else settings_str
                    clients = settings.get('clients', [])

                    for client in clients:
                        if client.get('email') == client_email:
                            logger.info(f"客户端 {client_email} 已存在于入站规则 {inbound.get('id')}")
                            return True

                except json.JSONDecodeError as e:
                    logger.warning(f"解析入站规则 {inbound.get('id')} 的settings失败: {e}")
                    continue

            return False

        except Exception as e:
            logger.error(f"检查客户端是否存在时发生错误: {str(e)}")
            return False

    def get_all_client_traffic(self) -> Optional[List[Dict]]:
        """获取所有客户端的流量使用情况"""
        if not self._ensure_logged_in():
            return None

        try:
            # 获取所有入站规则和客户端统计信息
            inbounds = self.get_inbounds()
            if not inbounds:
                logger.error("无法获取入站规则列表")
                return None

            all_client_stats = []

            # 遍历所有入站规则，收集客户端统计信息
            for inbound in inbounds:
                client_stats = inbound.get('clientStats', [])
                for client_stat in client_stats:
                    # 添加入站规则信息到客户端统计中
                    enhanced_stat = client_stat.copy()
                    enhanced_stat['inbound_id'] = inbound.get('id')
                    enhanced_stat['inbound_port'] = inbound.get('port')
                    enhanced_stat['inbound_protocol'] = inbound.get('protocol')
                    enhanced_stat['inbound_remark'] = inbound.get('remark')
                    enhanced_stat['inbound_up'] = inbound.get('up', 0)
                    enhanced_stat['inbound_down'] = inbound.get('down', 0)
                    enhanced_stat['inbound_total'] = inbound.get('total', 0)
                    all_client_stats.append(enhanced_stat)

            logger.info(f"成功获取 {len(all_client_stats)} 个客户端的流量统计")
            return all_client_stats

        except Exception as e:
            logger.error(f"获取所有客户端流量信息时发生错误: {str(e)}")
            return None

    def parse_client_stats(self, inbounds: List[Dict]) -> Dict[str, Dict]:
        """解析客户端统计信息，返回以邮箱为键的字典"""
        try:
            client_stats_dict = {}

            for inbound in inbounds:
                client_stats = inbound.get('clientStats', [])
                for client_stat in client_stats:
                    email = client_stat.get('email')
                    if email:
                        # 创建增强的客户端统计信息
                        enhanced_stat = {
                            'email': email,
                            'id': client_stat.get('id'),
                            'inboundId': client_stat.get('inboundId'),
                            'enable': client_stat.get('enable', True),
                            'up': client_stat.get('up', 0),
                            'down': client_stat.get('down', 0),
                            'total': client_stat.get('total', 0),
                            'expiryTime': client_stat.get('expiryTime', 0),
                            'reset': client_stat.get('reset', 0),
                            # 添加入站规则信息
                            'inbound_info': {
                                'id': inbound.get('id'),
                                'port': inbound.get('port'),
                                'protocol': inbound.get('protocol'),
                                'remark': inbound.get('remark'),
                                'enable': inbound.get('enable', True),
                                'up': inbound.get('up', 0),
                                'down': inbound.get('down', 0),
                                'total': inbound.get('total', 0)
                            }
                        }
                        client_stats_dict[email] = enhanced_stat

            logger.info(f"解析得到 {len(client_stats_dict)} 个客户端的统计信息")
            return client_stats_dict

        except Exception as e:
            logger.error(f"解析客户端统计信息时发生错误: {str(e)}")
            return {}
    
    def generate_vless_config(self, inbound_info: Dict, client_id: str,
                             client_email: str, server_name: str = None) -> str:
        """生成VLESS配置字符串（URL格式）"""
        try:
            import json

            server_address = self.base_url.replace('http://', '').replace('https://', '').split(':')[0]
            server_port = inbound_info.get('port', 443)

            # 获取真实的客户端UUID
            real_client_id = self._get_real_client_id(inbound_info, client_email)
            if not real_client_id:
                logger.warning(f"无法找到客户端 {client_email} 的真实UUID，使用提供的ID")
                real_client_id = client_id

            # 基础VLESS配置
            config = f"vless://{real_client_id}@{server_address}:{server_port}"

            # 解析streamSettings
            stream_settings_str = inbound_info.get('streamSettings', '{}')
            try:
                stream_settings = json.loads(stream_settings_str) if isinstance(stream_settings_str, str) else stream_settings_str
            except:
                stream_settings = {}

            # 解析传输协议和安全设置
            network = stream_settings.get('network', 'tcp')
            security = stream_settings.get('security', 'none')

            # 构建参数列表
            params = []

            # 基础参数
            params.append(f"security={security}")
            params.append("encryption=none")  # VLESS固定为none
            params.append(f"type={network}")

            # 根据不同的传输协议添加特定参数
            if network == 'tcp':
                tcp_settings = stream_settings.get('tcpSettings', {})
                header = tcp_settings.get('header', {})
                header_type = header.get('type', 'none')
                if header_type != 'none':
                    params.append(f"headerType={header_type}")

            elif network == 'ws':
                ws_settings = stream_settings.get('wsSettings', {})
                path = ws_settings.get('path', '/')
                params.append(f"path={path}")

                headers = ws_settings.get('headers', {})
                host = headers.get('Host', '')
                if host:
                    params.append(f"host={host}")

            elif network == 'grpc':
                grpc_settings = stream_settings.get('grpcSettings', {})
                service_name = grpc_settings.get('serviceName', '')
                if service_name:
                    params.append(f"serviceName={service_name}")

            # Reality协议特殊处理
            if security == 'reality':
                reality_settings = stream_settings.get('realitySettings', {})
                settings = reality_settings.get('settings', {})

                # 添加serverNames（使用第一个作为SNI）
                server_names = reality_settings.get('serverNames', [])
                if server_names:
                    params.append(f"sni={server_names[0]}")

                # 添加shortId（使用第一个）
                short_ids = reality_settings.get('shortIds', [])
                if short_ids:
                    params.append(f"sid={short_ids[0]}")

                # 添加fingerprint
                fingerprint = settings.get('fingerprint', 'chrome')
                params.append(f"fp={fingerprint}")

                # 添加公钥
                public_key = settings.get('publicKey', '')
                if public_key:
                    params.append(f"pbk={public_key}")

                # 添加spiderX
                spider_x = settings.get('spiderX', '/')
                params.append(f"spx={spider_x}")

                # 添加flow参数（如果有）
                flow = settings.get('flow', '')
                if flow:
                    params.append(f"flow={flow}")

            # TLS设置
            elif security == 'tls':
                tls_settings = stream_settings.get('tlsSettings', {})
                server_name_tls = tls_settings.get('serverName', '')
                if server_name_tls:
                    params.append(f"sni={server_name_tls}")

                alpn = tls_settings.get('alpn', [])
                if alpn:
                    params.append(f"alpn={','.join(alpn)}")

            # 组合参数
            param_string = "&".join(params)

            # 组合完整配置
            config += f"?{param_string}#{client_email}"

            return config

        except Exception as e:
            logger.error(f"生成VLESS配置时发生错误: {str(e)}")
            return f"vless://{client_id}@{server_address}:{server_port}?type=tcp&security=none&encryption=none#{client_email}"

    def _get_real_client_id(self, inbound_info: Dict, client_email: str) -> str:
        """从入站规则配置中获取真实的客户端UUID"""
        try:
            import json

            settings_str = inbound_info.get('settings', '{}')
            settings = json.loads(settings_str) if isinstance(settings_str, str) else settings_str

            clients = settings.get('clients', [])
            for client in clients:
                if client.get('email') == client_email:
                    return client.get('id', '')

            return ''

        except Exception as e:
            logger.error(f"获取真实客户端ID时发生错误: {str(e)}")
            return ''

    def delete_client(self, inbound_id: int, client_uuid: str) -> bool:
        """删除指定入站规则中的客户端

        Args:
            inbound_id: 入站规则ID
            client_uuid: 客户端UUID

        Returns:
            bool: 删除是否成功
        """
        if not self._ensure_logged_in():
            return False

        try:
            # 构建删除客户端的URL，使用配置中的路径模板
            delete_path = Config.XUI_DELETE_CLIENT_PATH.format(
                inbound_id=inbound_id,
                client_uuid=client_uuid
            )
            url = self._get_url(delete_path)

            # 设置请求头，模拟浏览器请求
            headers = {
                'Accept': 'application/json, text/plain, */*',
                'Accept-Encoding': 'gzip, deflate',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                'Content-Length': '0',
                'X-Requested-With': 'XMLHttpRequest',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            }

            # 发送POST请求删除客户端
            response = self.session.post(url, headers=headers)

            logger.info(f"删除客户端请求 - URL: {url}")
            logger.info(f"删除客户端请求 - 状态码: {response.status_code}")
            logger.debug(f"删除客户端请求 - 响应内容: {response.text}")

            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get('success', False):
                        logger.info(f"成功删除客户端: inbound_id={inbound_id}, client_uuid={client_uuid}")
                        return True
                    else:
                        logger.error(f"删除客户端失败: {result.get('msg', '未知错误')}")
                        return False
                except json.JSONDecodeError:
                    # 某些版本的X-UI可能返回非JSON响应，但状态码200表示成功
                    logger.warning("删除客户端响应不是JSON格式，但状态码为200，假设删除成功")
                    return True
            else:
                logger.error(f"删除客户端请求失败，状态码: {response.status_code}")
                logger.error(f"响应内容: {response.text}")
                return False

        except Exception as e:
            logger.error(f"删除客户端时发生错误: {str(e)}")
            return False

    def update_client_expiry_by_email(self, client_email: str, new_expiry_time: int) -> Tuple[bool, Optional[str]]:
        """根据客户端邮箱更新客户端到期时间

        Args:
            client_email: 客户端邮箱
            new_expiry_time: 新的到期时间（毫秒时间戳，0表示永不过期）

        Returns:
            Tuple[bool, Optional[str]]: (是否成功, 错误信息)
        """
        if not self._ensure_logged_in():
            return False, "未登录到X-UI面板"

        try:
            # 首先获取所有入站规则
            inbounds = self.get_inbounds()
            if not inbounds:
                return False, "无法获取入站规则列表"

            # 在所有入站规则中查找指定邮箱的客户端
            for inbound in inbounds:
                settings_str = inbound.get('settings', '{}')
                try:
                    settings = json.loads(settings_str) if isinstance(settings_str, str) else settings_str
                    clients = settings.get('clients', [])

                    for i, client in enumerate(clients):
                        if client.get('email') == client_email:
                            # 找到客户端，更新到期时间
                            inbound_id = inbound.get('id')
                            client_uuid = client.get('id')

                            if inbound_id and client_uuid:
                                success = self.update_client_expiry(inbound_id, client_uuid, new_expiry_time)
                                if success:
                                    return True, None
                                else:
                                    return False, f"更新客户端到期时间失败: inbound_id={inbound_id}, client_uuid={client_uuid}"
                            else:
                                return False, f"客户端信息不完整: inbound_id={inbound_id}, client_uuid={client_uuid}"

                except json.JSONDecodeError as e:
                    logger.warning(f"解析入站规则 {inbound.get('id')} 的settings失败: {e}")
                    continue

            return False, f"未找到邮箱为 {client_email} 的客户端"

        except Exception as e:
            logger.error(f"更新客户端到期时间时发生错误: {str(e)}")
            return False, f"更新客户端到期时间时发生错误: {str(e)}"

    def update_client_expiry(self, inbound_id: int, client_uuid: str, new_expiry_time: int) -> bool:
        """更新指定客户端的到期时间

        Args:
            inbound_id: 入站规则ID
            client_uuid: 客户端UUID
            new_expiry_time: 新的到期时间（毫秒时间戳，0表示永不过期）

        Returns:
            bool: 是否成功
        """
        if not self._ensure_logged_in():
            return False

        try:
            # 构建更新客户端到期时间的URL
            url = self._get_url(f"/panel/inbound/{inbound_id}/updateClient/{client_uuid}")

            # 设置请求头
            headers = {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json, text/plain, */*'
            }

            # 准备请求数据
            data = {
                'expiryTime': new_expiry_time
            }

            # 发送POST请求更新客户端
            response = self.session.post(url, data=data, headers=headers)

            logger.info(f"更新客户端到期时间请求 - URL: {url}")
            logger.info(f"更新客户端到期时间请求 - 状态码: {response.status_code}")
            logger.debug(f"更新客户端到期时间请求 - 响应内容: {response.text}")

            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get('success', False):
                        logger.info(f"成功更新客户端到期时间: inbound_id={inbound_id}, client_uuid={client_uuid}, new_expiry={new_expiry_time}")
                        return True
                    else:
                        logger.error(f"更新客户端到期时间失败: {result.get('msg', '未知错误')}")
                        return False
                except json.JSONDecodeError:
                    # 某些版本的X-UI可能返回非JSON响应，但状态码200表示成功
                    logger.warning("更新客户端到期时间响应不是JSON格式，但状态码为200，假设更新成功")
                    return True
            else:
                logger.error(f"更新客户端到期时间请求失败，状态码: {response.status_code}")
                logger.error(f"响应内容: {response.text}")
                return False

        except Exception as e:
            logger.error(f"更新客户端到期时间时发生错误: {str(e)}")
            return False

    def delete_client_by_email(self, client_email: str) -> Tuple[bool, Optional[str]]:
        """根据客户端邮箱删除客户端

        Args:
            client_email: 客户端邮箱

        Returns:
            Tuple[bool, Optional[str]]: (是否成功, 错误信息)
        """
        if not self._ensure_logged_in():
            return False, "未登录到X-UI面板"

        try:
            # 首先获取所有入站规则
            inbounds = self.get_inbounds()
            if not inbounds:
                return False, "无法获取入站规则列表"

            # 在所有入站规则中查找指定邮箱的客户端
            for inbound in inbounds:
                settings_str = inbound.get('settings', '{}')
                try:
                    settings = json.loads(settings_str) if isinstance(settings_str, str) else settings_str
                    clients = settings.get('clients', [])

                    for client in clients:
                        if client.get('email') == client_email:
                            # 找到客户端，执行删除
                            inbound_id = inbound.get('id')
                            client_uuid = client.get('id')

                            if inbound_id and client_uuid:
                                success = self.delete_client(inbound_id, client_uuid)
                                if success:
                                    return True, None
                                else:
                                    return False, f"删除客户端失败: inbound_id={inbound_id}, client_uuid={client_uuid}"
                            else:
                                return False, f"客户端信息不完整: inbound_id={inbound_id}, client_uuid={client_uuid}"

                except json.JSONDecodeError as e:
                    logger.warning(f"解析入站规则 {inbound.get('id')} 的settings失败: {e}")
                    continue

            return False, f"未找到邮箱为 {client_email} 的客户端"

        except Exception as e:
            logger.error(f"根据邮箱删除客户端时发生错误: {str(e)}")
            return False, f"删除过程中发生错误: {str(e)}"

    def add_clients_batch(self, inbound_id: int, clients: List[Dict]) -> Tuple[bool, Dict]:
        """
        批量添加客户端到指定入站协议

        Args:
            inbound_id: 入站协议ID
            clients: 客户端配置列表，每个客户端包含以下字段：
                - id: 客户端UUID
                - email: 客户端邮箱
                - totalGB: 流量限制（字节）
                - expiryTime: 过期时间（时间戳，毫秒）
                - limitIp: IP限制数量（可选，默认0）
                - enable: 是否启用（可选，默认True）
                - flow: 流控（可选，默认""）
                - tgId: Telegram ID（可选，默认""）
                - subId: 订阅ID（可选，默认""）
                - comment: 备注（可选，默认""）
                - reset: 重置（可选，默认0）
                - security: 安全设置（可选，默认"auto"）

        Returns:
            Tuple[bool, Dict]: (是否成功, 响应数据)
        """
        try:
            if not self._ensure_logged_in():
                return False, {"error": "登录失败"}

            # 构造客户端配置
            formatted_clients = []
            for client in clients:
                formatted_client = {
                    "id": client.get("id", ""),
                    "flow": client.get("flow", ""),
                    "email": client.get("email", ""),
                    "limitIp": client.get("limitIp", 0),
                    "totalGB": client.get("totalGB", 0),
                    "expiryTime": client.get("expiryTime", 0),
                    "enable": client.get("enable", True),
                    "tgId": client.get("tgId", ""),
                    "subId": client.get("subId", ""),
                    "comment": client.get("comment", ""),
                    "reset": client.get("reset", 0),
                    "security": client.get("security", "auto")
                }
                formatted_clients.append(formatted_client)

            # 构造settings JSON
            settings = {
                "clients": formatted_clients
            }

            # 准备请求数据
            data = {
                'id': str(inbound_id),
                'settings': json.dumps(settings, separators=(',', ':'))
            }

            # 发送请求
            url = self._get_url("/panel/inbound/addClient")
            headers = {
                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json, text/plain, */*',
                'Referer': self._get_url("/panel/inbounds")
            }

            response = self.session.post(url, data=data, headers=headers)

            logger.info(f"批量添加客户端请求 - URL: {url}")
            logger.info(f"批量添加客户端请求 - 状态码: {response.status_code}")
            logger.debug(f"批量添加客户端请求 - 响应内容: {response.text}")

            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get('success'):
                        logger.info(f"批量添加 {len(clients)} 个客户端到入站协议 {inbound_id} 成功")
                        return True, result
                    else:
                        error_msg = result.get('msg', '未知错误')
                        logger.error(f"批量添加客户端失败: {error_msg}")
                        return False, {"error": error_msg}
                except json.JSONDecodeError:
                    logger.warning("批量添加客户端响应不是JSON格式，但状态码为200，假设添加成功")
                    return True, {"success": True, "msg": "批量添加成功"}
            else:
                logger.error(f"批量添加客户端请求失败: HTTP {response.status_code}")
                logger.error(f"响应内容: {response.text}")
                return False, {"error": f"HTTP {response.status_code}"}

        except Exception as e:
            logger.error(f"批量添加客户端时发生错误: {str(e)}")
            return False, {"error": str(e)}
