{% extends "base.html" %}

{% block title %}分组管理 - 管理后台{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>
                    <i class="bi bi-collection"></i> X-UI面板分组管理
                </h1>
                <div>
                    <a href="{{ url_for('admin.create_group') }}" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> 创建分组
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 分组列表 -->
    <div class="row">
        {% for group in groups %}
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <span class="badge me-2" style="background-color: {{ group.color }}; color: white;">
                            {{ group.priority }}
                        </span>
                        {{ group.name }}
                    </h6>
                    <span class="badge bg-{{ 'success' if group.is_active else 'secondary' }}">
                        {{ '活跃' if group.is_active else '停用' }}
                    </span>
                </div>
                <div class="card-body">
                    {% if group.description %}
                    <p class="card-text text-muted">{{ group.description }}</p>
                    {% endif %}
                    
                    <div class="row text-center">
                        <div class="col-6">
                            <small class="text-muted">面板数量</small>
                            <div class="fw-bold">{{ group.panels|length }}</div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">活跃面板</small>
                            <div class="fw-bold">{{ group.active_panels|length }}</div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row text-center">
                        <div class="col-6">
                            <small class="text-muted">优先级</small>
                            <div class="fw-bold">{{ group.priority }}</div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">关联产品</small>
                            <div class="fw-bold">{{ group.products|length }}</div>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="btn-group w-100" role="group">
                        <a href="{{ url_for('admin.group_panels', group_id=group.id) }}" class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-server"></i> 管理面板
                        </a>
                        <button type="button" class="btn btn-outline-success btn-sm" onclick="batchSyncGroup({{ group.id }})">
                            <i class="bi bi-rocket"></i> 批量同步
                        </button>
                        <a href="{{ url_for('admin.edit_group', group_id=group.id) }}" class="btn btn-outline-secondary btn-sm">
                            <i class="bi bi-pencil"></i> 编辑
                        </a>
                        <form method="POST" action="{{ url_for('admin.delete_group', group_id=group.id) }}"
                              style="display: inline;" onsubmit="return confirm('确定要删除分组 {{ group.name }} 吗？')">
                            <button type="submit" class="btn btn-outline-danger btn-sm">
                                <i class="bi bi-trash"></i> 删除
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
        
        {% if not groups %}
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="bi bi-collection text-muted" style="font-size: 4rem;"></i>
                    <h4 class="text-muted mt-3">暂无分组</h4>
                    <p class="text-muted">还没有创建任何面板分组</p>
                    <a href="{{ url_for('admin.create_group') }}" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> 创建第一个分组
                    </a>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
    
    <!-- 返回按钮 -->
    <div class="row mt-4">
        <div class="col-12">
            <a href="{{ url_for('admin.dashboard') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回仪表板
            </a>
        </div>
    </div>
</div>

<style>
.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.badge {
    font-size: 0.75em;
}
</style>

<script>
function batchSyncGroup(groupId) {
    if (!confirm('确定要批量同步该分组的所有订阅吗？这将把所有活跃订阅同步到分组中的所有面板。')) {
        return;
    }

    // 显示加载状态
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="bi bi-arrow-repeat spin"></i> 同步中...';
    button.disabled = true;

    // 发送批量同步请求
    fetch(`/admin/api/groups/${groupId}/batch-sync`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(`批量同步完成！\n成功: ${data.success_count}\n失败: ${data.failed_count}\n影响订阅: ${data.affected_subscriptions}`);
            location.reload(); // 刷新页面
        } else {
            alert(`批量同步失败: ${data.error || '未知错误'}`);
        }
    })
    .catch(error => {
        console.error('批量同步错误:', error);
        alert('批量同步过程中发生错误，请查看控制台日志');
    })
    .finally(() => {
        // 恢复按钮状态
        button.innerHTML = originalText;
        button.disabled = false;
    });
}
</script>

<style>
.spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}
</style>

{% endblock %}
