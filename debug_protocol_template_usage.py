#!/usr/bin/env python3
"""
调试协议模板使用过程
"""
from app import create_app
from models import db, XUIPanel, XUIPanelGroupMembership, ProtocolTemplate
from services.protocol_template_service import ProtocolTemplateService

def main():
    app = create_app()
    with app.app_context():
        print('=== 调试协议模板使用过程 ===\n')
        
        # 1. 模拟订单服务中的逻辑
        print('1. 模拟订单服务逻辑:')
        
        # 模拟面板ID
        panel_id = "panel_1"  # 这是配置服务中的键
        
        # 模拟从配置服务获取面板信息
        from services.config_service import ConfigService
        config_service = ConfigService()
        panels_config = config_service.get_xui_panels()
        
        if panel_id in panels_config:
            panel_config = panels_config[panel_id]
            panel_db_id = panel_config.get('panel_id')
            print(f'   面板键: {panel_id}')
            print(f'   面板名称: {panel_config.get("name")}')
            print(f'   数据库ID: {panel_db_id}')
            
            # 2. 查找面板在数据库中的记录
            print(f'\n2. 查找面板数据库记录:')
            panel = XUIPanel.query.get(panel_db_id)
            if panel:
                print(f'   ✓ 找到面板: {panel.name} (ID: {panel.id})')
                
                # 3. 查找面板的分组成员关系
                print(f'\n3. 查找分组成员关系:')
                membership = XUIPanelGroupMembership.query.filter_by(panel_id=panel.id).first()
                if membership:
                    print(f'   ✓ 找到成员关系: 面板ID {membership.panel_id} -> 分组ID {membership.group_id}')
                    print(f'   入站ID: {membership.inbound_id}')
                    print(f'   协议模板ID: {membership.protocol_template_id}')
                    
                    # 4. 检查协议模板
                    if membership.protocol_template_id:
                        print(f'\n4. 检查协议模板:')
                        template = ProtocolTemplate.query.get(membership.protocol_template_id)
                        if template and template.is_active:
                            print(f'   ✓ 找到活跃模板: {template.name}')
                            print(f'   协议类型: {template.protocol_type.value}')
                            print(f'   自定义协议名称: {template.custom_protocol_name}')
                            print(f'   模板内容: {template.template_content}')
                            
                            # 5. 测试配置生成
                            print(f'\n5. 测试配置生成:')
                            
                            # 模拟入站配置
                            mock_inbound_config = {
                                'id': 1,
                                'port': 36712,
                                'protocol': 'vless',
                                'streamSettings': '{"network":"tcp","security":"tls","tlsSettings":{"serverName":"example.com","alpn":["h2","http/1.1"]}}'
                            }
                            
                            # 模拟客户端数据
                            mock_client_data = {
                                'id': 'test-client-id-12345',
                                'email': '<EMAIL>'
                            }
                            
                            # 准备面板配置信息
                            panel_config_for_template = {
                                'server_address': panel.base_url.replace('http://', '').replace('https://', '').split(':')[0],
                                'base_url': panel.base_url,
                                'name': panel.name
                            }
                            
                            # 使用协议模板服务生成配置
                            template_service = ProtocolTemplateService()
                            config = template_service.generate_config(
                                template.id, mock_inbound_config, panel_config_for_template, mock_client_data
                            )
                            
                            if config:
                                print(f'   ✓ 配置生成成功:')
                                print(f'   {config}')
                                
                                # 检查配置类型
                                if config.startswith('hysteria://'):
                                    print(f'   ✓ 确认使用了自定义协议模板 (Hysteria)')
                                elif config.startswith('vless://'):
                                    print(f'   ℹ 使用了VLESS协议')
                                else:
                                    print(f'   ? 其他协议类型')
                            else:
                                print(f'   ✗ 配置生成失败')
                        else:
                            print(f'   ✗ 协议模板不存在或未激活')
                    else:
                        print(f'\n4. 无协议模板配置，将使用默认VLESS')
                else:
                    print(f'   ✗ 未找到分组成员关系')
            else:
                print(f'   ✗ 未找到面板数据库记录')
        else:
            print(f'   ✗ 面板键 {panel_id} 不存在于配置中')
        
        print(f'\n=== 调试完成 ===')

if __name__ == '__main__':
    main()
