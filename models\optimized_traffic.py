"""
优化的流量统计模型 - 双表设计
解决多订阅时的性能问题
"""
from models import db
from datetime import datetime
from enum import Enum

class AggregationType(Enum):
    """聚合类型"""
    HOURLY = 'hourly'
    DAILY = 'daily'
    WEEKLY = 'weekly'
    MONTHLY = 'monthly'

class CurrentTrafficStats(db.Model):
    """
    实时流量统计表
    每个订阅只有一条记录，不断更新
    用于实时查询和计费
    """
    __tablename__ = 'current_traffic_stats'
    
    # 主键就是订阅ID，确保每个订阅只有一条记录
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscriptions.id'), primary_key=True)
    
    # 流量数据（累计值）
    upload_bytes = db.Column(db.BigInteger, default=0, nullable=False)
    download_bytes = db.Column(db.BigInteger, default=0, nullable=False)
    total_bytes = db.Column(db.BigInteger, default=0, nullable=False)
    
    # 时间戳
    last_updated = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    # 关联关系
    subscription = db.relationship('Subscription', backref='current_traffic_stat')
    
    def __repr__(self):
        return f'<CurrentTrafficStats subscription_id={self.subscription_id} total_bytes={self.total_bytes}>'
    
    @property
    def total_gb(self):
        """总流量（GB）"""
        return self.total_bytes / (1024 ** 3) if self.total_bytes else 0
    
    @property
    def upload_gb(self):
        """上传流量（GB）"""
        return self.upload_bytes / (1024 ** 3) if self.upload_bytes else 0
    
    @property
    def download_gb(self):
        """下载流量（GB）"""
        return self.download_bytes / (1024 ** 3) if self.download_bytes else 0

class HistoricalTrafficStats(db.Model):
    """
    历史流量统计表
    定期聚合的历史数据
    用于趋势分析和报表
    """
    __tablename__ = 'historical_traffic_stats'
    
    id = db.Column(db.Integer, primary_key=True)
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscriptions.id'), nullable=False)
    
    # 流量数据（聚合值）
    upload_bytes = db.Column(db.BigInteger, default=0, nullable=False)
    download_bytes = db.Column(db.BigInteger, default=0, nullable=False)
    total_bytes = db.Column(db.BigInteger, default=0, nullable=False)
    
    # 时间范围
    period_start = db.Column(db.DateTime, nullable=False)
    period_end = db.Column(db.DateTime, nullable=False)
    
    # 聚合类型
    aggregation_type = db.Column(db.Enum(AggregationType), nullable=False)
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    # 关联关系
    subscription = db.relationship('Subscription', backref='historical_traffic_stats')
    
    # 索引优化
    __table_args__ = (
        db.Index('idx_subscription_period', 'subscription_id', 'period_start', 'period_end'),
        db.Index('idx_aggregation_type', 'aggregation_type'),
        db.Index('idx_period_start', 'period_start'),
    )
    
    def __repr__(self):
        return f'<HistoricalTrafficStats subscription_id={self.subscription_id} type={self.aggregation_type.value} total_bytes={self.total_bytes}>'
    
    @property
    def total_gb(self):
        """总流量（GB）"""
        return self.total_bytes / (1024 ** 3) if self.total_bytes else 0
    
    @property
    def upload_gb(self):
        """上传流量（GB）"""
        return self.upload_bytes / (1024 ** 3) if self.upload_bytes else 0
    
    @property
    def download_gb(self):
        """下载流量（GB）"""
        return self.download_bytes / (1024 ** 3) if self.download_bytes else 0
    
    @property
    def duration_hours(self):
        """时间跨度（小时）"""
        if self.period_start and self.period_end:
            return (self.period_end - self.period_start).total_seconds() / 3600
        return 0

class TrafficStatsLegacy(db.Model):
    """
    原有流量统计表（重命名为legacy）
    保留作为备份和迁移参考
    """
    __tablename__ = 'traffic_stats_legacy'
    
    id = db.Column(db.Integer, primary_key=True)
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscriptions.id'), nullable=False)
    
    upload_bytes = db.Column(db.BigInteger, default=0)
    download_bytes = db.Column(db.BigInteger, default=0)
    total_bytes = db.Column(db.BigInteger, default=0)
    
    recorded_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    subscription = db.relationship('Subscription', backref='legacy_traffic_stats')
    
    def __repr__(self):
        return f'<TrafficStatsLegacy subscription_id={self.subscription_id} total_bytes={self.total_bytes}>'
