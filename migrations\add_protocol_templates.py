#!/usr/bin/env python3
"""
数据库迁移脚本：添加协议模板功能
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app
from models import db, ProtocolTemplate
from services.protocol_template_service import ProtocolTemplateService
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def migrate_add_protocol_templates():
    """添加协议模板功能的数据库迁移"""
    app = create_app()
    
    with app.app_context():
        try:
            logger.info("开始数据库迁移：添加协议模板功能")
            
            # 创建新表
            logger.info("创建协议模板表...")
            db.create_all()
            
            # 添加protocol_template_id列到xui_panel_group_memberships表
            logger.info("检查并添加protocol_template_id列...")
            
            # 检查列是否已存在
            inspector = db.inspect(db.engine)
            columns = [col['name'] for col in inspector.get_columns('xui_panel_group_memberships')]
            
            if 'protocol_template_id' not in columns:
                logger.info("添加protocol_template_id列到xui_panel_group_memberships表...")
                with db.engine.connect() as conn:
                    conn.execute(db.text('''
                        ALTER TABLE xui_panel_group_memberships
                        ADD COLUMN protocol_template_id INTEGER REFERENCES protocol_templates(id)
                    '''))
                    conn.commit()
                logger.info("protocol_template_id列添加成功")
            else:
                logger.info("protocol_template_id列已存在，跳过添加")
            
            # 创建默认协议模板
            logger.info("创建默认协议模板...")
            template_service = ProtocolTemplateService()
            success = template_service.create_default_templates()
            
            if success:
                logger.info("默认协议模板创建成功")
            else:
                logger.warning("默认协议模板创建失败")
            
            logger.info("数据库迁移完成！")
            
        except Exception as e:
            logger.error(f"数据库迁移失败: {e}")
            db.session.rollback()
            raise

def rollback_protocol_templates():
    """回滚协议模板功能（仅用于开发测试）"""
    app = create_app()
    
    with app.app_context():
        try:
            logger.info("开始回滚协议模板功能...")
            
            # 删除protocol_template_id列的外键约束
            logger.info("移除protocol_template_id列...")
            try:
                with db.engine.connect() as conn:
                    conn.execute(db.text('''
                        ALTER TABLE xui_panel_group_memberships
                        DROP COLUMN protocol_template_id
                    '''))
                    conn.commit()
                logger.info("protocol_template_id列移除成功")
            except Exception as e:
                logger.warning(f"移除protocol_template_id列失败: {e}")

            # 删除协议模板表
            logger.info("删除协议模板表...")
            try:
                with db.engine.connect() as conn:
                    conn.execute(db.text('DROP TABLE IF EXISTS protocol_templates'))
                    conn.commit()
                logger.info("协议模板表删除成功")
            except Exception as e:
                logger.warning(f"删除协议模板表失败: {e}")
            
            logger.info("协议模板功能回滚完成")
            
        except Exception as e:
            logger.error(f"回滚失败: {e}")
            raise

def check_migration_status():
    """检查迁移状态"""
    app = create_app()
    
    with app.app_context():
        try:
            # 检查协议模板表是否存在
            inspector = db.inspect(db.engine)
            tables = inspector.get_table_names()
            
            if 'protocol_templates' in tables:
                logger.info("✓ 协议模板表已存在")
                
                # 检查默认模板数量
                template_count = ProtocolTemplate.query.count()
                default_count = ProtocolTemplate.query.filter_by(is_default=True).count()
                logger.info(f"✓ 协议模板总数: {template_count}, 默认模板数: {default_count}")
            else:
                logger.info("✗ 协议模板表不存在")
            
            # 检查protocol_template_id列是否存在
            columns = [col['name'] for col in inspector.get_columns('xui_panel_group_memberships')]
            if 'protocol_template_id' in columns:
                logger.info("✓ protocol_template_id列已存在")
            else:
                logger.info("✗ protocol_template_id列不存在")
                
        except Exception as e:
            logger.error(f"检查迁移状态失败: {e}")

if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='协议模板功能数据库迁移')
    parser.add_argument('--rollback', action='store_true', help='回滚协议模板功能（仅用于开发测试）')
    parser.add_argument('--status', action='store_true', help='检查迁移状态')
    
    args = parser.parse_args()
    
    if args.status:
        check_migration_status()
    elif args.rollback:
        confirm = input("确定要回滚协议模板功能吗？这将删除所有协议模板数据！(yes/no): ")
        if confirm.lower() == 'yes':
            rollback_protocol_templates()
        else:
            print("回滚操作已取消")
    else:
        migrate_add_protocol_templates()
