#!/usr/bin/env python3
"""
修复数据库中的枚举值问题
"""
from app import create_app
from models import db
from sqlalchemy import text
import os

def fix_enum_values():
    """修复数据库中的枚举值问题"""
    app = create_app()
    with app.app_context():
        print('=== 修复数据库枚举值问题 ===\n')
        
        # 1. 检查当前数据库
        print('1. 检查当前数据库:')
        
        db_path = app.config.get('SQLALCHEMY_DATABASE_URI', '').replace('sqlite:///', '')
        print(f'   数据库文件: {db_path}')
        
        if os.path.exists(db_path):
            db_size = os.path.getsize(db_path) / (1024 * 1024)  # MB
            print(f'   文件大小: {db_size:.2f} MB')
        else:
            print(f'   ❌ 数据库文件不存在')
            return
        
        # 2. 检查面板状态枚举值
        print(f'\n2. 检查面板状态枚举值:')
        
        try:
            # 查看当前面板状态值
            result = db.session.execute(text("SELECT id, name, status FROM xui_panels"))
            panels = list(result)
            
            print(f'   当前面板数据:')
            for panel in panels:
                panel_id, name, status = panel
                print(f'     面板 {panel_id}: {name} - 状态: "{status}"')
            
            # 检查是否有小写状态值
            result = db.session.execute(text("SELECT COUNT(*) FROM xui_panels WHERE status = 'active'"))
            active_count = result.scalar()
            
            result = db.session.execute(text("SELECT COUNT(*) FROM xui_panels WHERE status = 'inactive'"))
            inactive_count = result.scalar()
            
            result = db.session.execute(text("SELECT COUNT(*) FROM xui_panels WHERE status = 'maintenance'"))
            maintenance_count = result.scalar()
            
            print(f'\n   小写状态统计:')
            print(f'     active: {active_count} 个')
            print(f'     inactive: {inactive_count} 个')
            print(f'     maintenance: {maintenance_count} 个')
            
        except Exception as e:
            print(f'   查询面板状态失败: {e}')
            return
        
        # 3. 修复枚举值
        print(f'\n3. 修复枚举值:')
        
        total_fixed = 0
        
        # 修复 active -> ACTIVE
        if active_count > 0:
            try:
                updated = db.session.execute(text("UPDATE xui_panels SET status = 'ACTIVE' WHERE status = 'active'")).rowcount
                print(f'   ✓ 修复 active -> ACTIVE: {updated} 个')
                total_fixed += updated
            except Exception as e:
                print(f'   修复 active 失败: {e}')
        
        # 修复 inactive -> INACTIVE
        if inactive_count > 0:
            try:
                updated = db.session.execute(text("UPDATE xui_panels SET status = 'INACTIVE' WHERE status = 'inactive'")).rowcount
                print(f'   ✓ 修复 inactive -> INACTIVE: {updated} 个')
                total_fixed += updated
            except Exception as e:
                print(f'   修复 inactive 失败: {e}')
        
        # 修复 maintenance -> MAINTENANCE
        if maintenance_count > 0:
            try:
                updated = db.session.execute(text("UPDATE xui_panels SET status = 'MAINTENANCE' WHERE status = 'maintenance'")).rowcount
                print(f'   ✓ 修复 maintenance -> MAINTENANCE: {updated} 个')
                total_fixed += updated
            except Exception as e:
                print(f'   修复 maintenance 失败: {e}')
        
        # 4. 检查其他可能的枚举问题
        print(f'\n4. 检查其他枚举问题:')
        
        # 检查产品类型
        try:
            result = db.session.execute(text("SELECT DISTINCT product_type FROM products"))
            product_types = [row[0] for row in result]
            print(f'   产品类型: {product_types}')
            
            # 检查是否需要修复产品类型
            for product_type in product_types:
                if product_type and product_type.islower():
                    upper_type = product_type.upper()
                    updated = db.session.execute(text(f"UPDATE products SET product_type = '{upper_type}' WHERE product_type = '{product_type}'")).rowcount
                    if updated > 0:
                        print(f'   ✓ 修复产品类型 {product_type} -> {upper_type}: {updated} 个')
                        total_fixed += updated
                        
        except Exception as e:
            print(f'   检查产品类型失败: {e}')
        
        # 检查节点类型
        try:
            result = db.session.execute(text("SELECT DISTINCT node_type FROM products"))
            node_types = [row[0] for row in result]
            print(f'   节点类型: {node_types}')
            
            # 检查是否需要修复节点类型
            for node_type in node_types:
                if node_type and node_type.islower():
                    upper_type = node_type.upper()
                    updated = db.session.execute(text(f"UPDATE products SET node_type = '{upper_type}' WHERE node_type = '{node_type}'")).rowcount
                    if updated > 0:
                        print(f'   ✓ 修复节点类型 {node_type} -> {upper_type}: {updated} 个')
                        total_fixed += updated
                        
        except Exception as e:
            print(f'   检查节点类型失败: {e}')
        
        # 检查用户角色
        try:
            result = db.session.execute(text("SELECT DISTINCT role FROM users"))
            user_roles = [row[0] for row in result]
            print(f'   用户角色: {user_roles}')
            
            # 检查是否需要修复用户角色
            for role in user_roles:
                if role and role.islower():
                    upper_role = role.upper()
                    updated = db.session.execute(text(f"UPDATE users SET role = '{upper_role}' WHERE role = '{role}'")).rowcount
                    if updated > 0:
                        print(f'   ✓ 修复用户角色 {role} -> {upper_role}: {updated} 个')
                        total_fixed += updated
                        
        except Exception as e:
            print(f'   检查用户角色失败: {e}')
        
        # 5. 提交更改
        if total_fixed > 0:
            print(f'\n5. 提交数据库更改:')
            try:
                db.session.commit()
                print(f'   ✓ 成功提交所有更改')
            except Exception as e:
                print(f'   ❌ 提交失败: {e}')
                db.session.rollback()
                return
        else:
            print(f'\n5. 无需修复:')
            print(f'   ✓ 所有枚举值都是正确的')
        
        # 6. 验证修复结果
        print(f'\n6. 验证修复结果:')
        
        try:
            # 重新查看面板状态
            result = db.session.execute(text("SELECT id, name, status FROM xui_panels"))
            panels = list(result)
            
            print(f'   修复后的面板数据:')
            for panel in panels:
                panel_id, name, status = panel
                print(f'     面板 {panel_id}: {name} - 状态: "{status}"')
            
            # 检查是否还有小写值
            result = db.session.execute(text("SELECT COUNT(*) FROM xui_panels WHERE status IN ('active', 'inactive', 'maintenance')"))
            remaining_lowercase = result.scalar()
            
            if remaining_lowercase == 0:
                print(f'   ✅ 所有面板状态已修复为大写')
            else:
                print(f'   ⚠️ 仍有 {remaining_lowercase} 个小写状态值')
                
        except Exception as e:
            print(f'   验证修复结果失败: {e}')
        
        # 7. 测试应用程序兼容性
        print(f'\n7. 测试应用程序兼容性:')
        
        try:
            # 尝试使用模型查询面板
            from models import XUIPanel
            panels = XUIPanel.query.all()
            print(f'   ✓ 成功查询到 {len(panels)} 个面板')
            
            for panel in panels:
                print(f'     面板: {panel.name} - 状态: {panel.status}')
                
        except Exception as e:
            print(f'   ❌ 模型查询失败: {e}')
            print(f'   可能需要重启应用程序')
        
        # 8. 总结
        print(f'\n8. 修复总结:')
        
        print(f'   总修复项目: {total_fixed} 项')
        
        if total_fixed > 0:
            print(f'   🎉 枚举值修复完成！')
            print(f'   📋 建议重启应用程序以确保更改生效')
        else:
            print(f'   ✅ 枚举值都是正确的，无需修复')
        
        print(f'\n=== 枚举值修复完成 ===')

if __name__ == '__main__':
    fix_enum_values()
