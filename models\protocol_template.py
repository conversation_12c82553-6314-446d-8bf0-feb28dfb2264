"""
协议模板模型
"""
from datetime import datetime
from . import db
from .enums import NodeType


class ProtocolTemplate(db.Model):
    """协议模板模型 - 管理自定义协议配置模板"""
    __tablename__ = 'protocol_templates'

    id = db.<PERSON>umn(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    description = db.Column(db.Text, nullable=True)
    
    # 协议类型
    protocol_type = db.Column(db.Enum(NodeType), nullable=False)

    # 自定义协议名称（当protocol_type为CUSTOM时使用）
    custom_protocol_name = db.Column(db.String(50), nullable=True)

    # 模板内容（包含占位符的配置模板）
    template_content = db.Column(db.Text, nullable=False)
    
    # 模板变量说明（JSON格式）
    template_variables = db.Column(db.Text, nullable=True)
    
    # 状态
    is_active = db.Column(db.<PERSON>, nullable=False, default=True)
    is_default = db.Column(db.<PERSON>, nullable=False, default=False)
    
    # 时间字段
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<ProtocolTemplate {self.name}>'

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'protocol_type': self.protocol_type.value if self.protocol_type else None,
            'custom_protocol_name': self.custom_protocol_name,
            'template_content': self.template_content,
            'template_variables': self.template_variables,
            'is_active': self.is_active,
            'is_default': self.is_default,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

    @property
    def protocol_type_display(self):
        """获取协议类型显示名称"""
        if self.protocol_type == NodeType.VLESS:
            return "VLESS"
        elif self.protocol_type == NodeType.VMESS:
            return "VMess"
        elif self.protocol_type == NodeType.TROJAN:
            return "Trojan"
        elif self.protocol_type == NodeType.CUSTOM:
            return self.custom_protocol_name or "自定义"
        return "Unknown"

    def get_required_placeholders(self):
        """获取模板中的必需占位符"""
        import re
        placeholders = re.findall(r'\{([^}]+)\}', self.template_content)
        return list(set(placeholders))

    def validate_template(self):
        """验证模板内容的有效性"""
        # 基础必需占位符
        required_placeholders = ['client_id', 'server_address', 'server_port', 'client_email']

        # 自定义协议类型需要验证自定义协议名称
        if self.protocol_type == NodeType.CUSTOM and not self.custom_protocol_name:
            return False, ['custom_protocol_name']

        template_placeholders = self.get_required_placeholders()

        missing_placeholders = []
        for placeholder in required_placeholders:
            if placeholder not in template_placeholders:
                missing_placeholders.append(placeholder)

        return len(missing_placeholders) == 0, missing_placeholders

    @classmethod
    def get_default_templates(cls):
        """获取默认协议模板定义"""
        return [
            {
                'name': 'VLESS-TCP-NONE',
                'description': 'VLESS over TCP without TLS (基础配置)',
                'protocol_type': NodeType.VLESS,
                'template_content': 'vless://{client_id}@{server_address}:{server_port}?security=none&encryption=none&type=tcp#{client_email}',
                'is_default': True
            },
            {
                'name': 'VLESS-TCP-TLS',
                'description': 'VLESS over TCP with TLS (推荐配置)',
                'protocol_type': NodeType.VLESS,
                'template_content': 'vless://{client_id}@{server_address}:{server_port}?security=tls&encryption=none&type=tcp&sni={server_name}&alpn=h2,http/1.1#{client_email}',
                'is_default': True
            },
            {
                'name': 'VLESS-WS-TLS',
                'description': 'VLESS over WebSocket with TLS (CDN友好)',
                'protocol_type': NodeType.VLESS,
                'template_content': 'vless://{client_id}@{server_address}:{server_port}?security=tls&encryption=none&type=ws&path={ws_path}&host={ws_host}&sni={server_name}#{client_email}',
                'is_default': True
            },
            {
                'name': 'VMESS-TCP-TLS',
                'description': 'VMess over TCP with TLS (经典配置)',
                'protocol_type': NodeType.VMESS,
                'template_content': 'vmess://{vmess_config_base64}',
                'is_default': True
            },
            {
                'name': 'VMESS-WS-TLS',
                'description': 'VMess over WebSocket with TLS (CDN友好)',
                'protocol_type': NodeType.VMESS,
                'template_content': 'vmess://{vmess_config_base64}',
                'is_default': True
            },
            {
                'name': 'TROJAN-TCP-TLS',
                'description': 'Trojan over TCP with TLS (高性能)',
                'protocol_type': NodeType.TROJAN,
                'template_content': 'trojan://{client_id}@{server_address}:{server_port}?security=tls&type=tcp&sni={server_name}#{client_email}',
                'is_default': True
            }
        ]
