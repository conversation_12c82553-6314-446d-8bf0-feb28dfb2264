#!/usr/bin/env python3
"""
测试自动数据清理功能
"""
from app import create_app
from models import db, XUIPanel, XUIPanelGroup, XUIPanelGroupMembership, NodeConfig, Subscription, PanelStatus
import uuid
import requests
import time

def test_auto_cleanup():
    """测试自动数据清理功能"""
    app = create_app()
    with app.app_context():
        print('=== 自动数据清理功能测试 ===\n')
        
        # 1. 检查当前状态
        print('1. 检查当前系统状态:')
        
        panels = XUIPanel.query.all()
        groups = XUIPanelGroup.query.all()
        node_configs = NodeConfig.query.filter_by(is_active=True).all()
        
        print(f'   面板数量: {len(panels)}')
        print(f'   分组数量: {len(groups)}')
        print(f'   活跃节点配置: {len(node_configs)}')
        
        if not panels or not groups:
            print('❌ 系统中没有足够的面板或分组进行测试')
            return
        
        test_group = groups[0]
        print(f'   测试分组: {test_group.name}')
        
        # 2. 创建测试面板
        print(f'\n2. 创建测试面板:')
        
        test_panel = XUIPanel(
            name=f'测试面板_{uuid.uuid4().hex[:8]}',
            base_url='http://test.cleanup.panel:54321',
            username='admin',
            password='admin123',
            status=PanelStatus.ACTIVE
        )
        
        db.session.add(test_panel)
        db.session.commit()
        print(f'   创建测试面板: {test_panel.name} (ID: {test_panel.id})')
        
        # 3. 将面板添加到分组
        print(f'\n3. 将面板添加到分组:')
        
        membership = XUIPanelGroupMembership(
            group_id=test_group.id,
            panel_id=test_panel.id
        )
        
        db.session.add(membership)
        db.session.commit()
        print(f'   面板 {test_panel.name} 已添加到分组 {test_group.name}')
        
        # 4. 创建测试节点配置
        print(f'\n4. 创建测试节点配置:')
        
        test_server_address = 'test.cleanup.panel'
        test_configs = []
        
        for i in range(3):
            config = NodeConfig(
                order_id=1,  # 假设存在的订单ID
                server_address=test_server_address,
                server_port=443,
                client_email=f'test_cleanup_{i}_{uuid.uuid4().hex[:8]}@example.com',
                client_id=str(uuid.uuid4()),
                vless_config=f'vless://test-cleanup-config-{i}',
                is_active=True
            )
            test_configs.append(config)
            db.session.add(config)
        
        db.session.commit()
        print(f'   创建了 {len(test_configs)} 个测试节点配置')
        for config in test_configs:
            print(f'     - 配置ID: {config.id}, 邮箱: {config.client_email}')
        
        # 5. 验证数据创建成功
        print(f'\n5. 验证测试数据:')
        
        active_configs_before = NodeConfig.query.filter(
            NodeConfig.server_address == test_server_address,
            NodeConfig.is_active == True
        ).count()
        
        memberships_before = XUIPanelGroupMembership.query.filter_by(panel_id=test_panel.id).count()
        
        print(f'   测试面板的活跃节点配置: {active_configs_before}')
        print(f'   测试面板的分组关系: {memberships_before}')
        
        # 6. 测试从分组中移除面板（自动清理）
        print(f'\n6. 测试从分组中移除面板（应自动清理孤儿数据）:')
        
        try:
            # 模拟管理员操作：从分组中移除面板
            print(f'   模拟从分组 {test_group.id} 中移除面板 {test_panel.id}...')
            
            # 直接调用数据库操作（模拟路由逻辑）
            membership_to_delete = XUIPanelGroupMembership.query.filter_by(
                group_id=test_group.id,
                panel_id=test_panel.id
            ).first()
            
            if membership_to_delete:
                # 删除分组关系
                db.session.delete(membership_to_delete)
                
                # 自动清理孤儿数据（模拟路由中的逻辑）
                orphaned_configs = NodeConfig.query.filter(
                    NodeConfig.server_address == test_server_address,
                    NodeConfig.is_active == True
                ).all()
                
                cleaned_configs = 0
                for config in orphaned_configs:
                    # 检查该节点配置是否还有其他活跃的面板支持
                    other_panels = XUIPanel.query.filter(
                        XUIPanel.id != test_panel.id,
                        XUIPanel.status == PanelStatus.ACTIVE
                    ).all()
                    
                    has_support = False
                    for other_panel in other_panels:
                        other_address = other_panel.base_url.replace('http://', '').replace('https://', '').split(':')[0]
                        if other_address == test_server_address:
                            has_support = True
                            break
                    
                    # 如果没有其他面板支持，则停用该节点配置
                    if not has_support:
                        config.is_active = False
                        cleaned_configs += 1
                        print(f'     停用孤儿节点配置: {config.id}')
                
                db.session.commit()
                print(f'   ✓ 从分组移除面板成功，自动清理了 {cleaned_configs} 个孤儿节点配置')
            else:
                print('   ❌ 未找到分组关系')
                
        except Exception as e:
            print(f'   ❌ 从分组移除面板失败: {e}')
            db.session.rollback()
        
        # 7. 验证清理效果
        print(f'\n7. 验证清理效果:')
        
        active_configs_after = NodeConfig.query.filter(
            NodeConfig.server_address == test_server_address,
            NodeConfig.is_active == True
        ).count()
        
        memberships_after = XUIPanelGroupMembership.query.filter_by(panel_id=test_panel.id).count()
        
        print(f'   清理前活跃节点配置: {active_configs_before}')
        print(f'   清理后活跃节点配置: {active_configs_after}')
        print(f'   清理前分组关系: {memberships_before}')
        print(f'   清理后分组关系: {memberships_after}')
        
        if active_configs_after == 0:
            print('   ✓ 孤儿节点配置清理成功')
        else:
            print('   ⚠ 孤儿节点配置清理可能不完整')
        
        if memberships_after == 0:
            print('   ✓ 分组关系清理成功')
        else:
            print('   ⚠ 分组关系清理可能不完整')
        
        # 8. 测试完全删除面板（自动清理）
        print(f'\n8. 测试完全删除面板（应自动清理所有相关数据）:')
        
        # 重新创建一些测试数据
        for i in range(2):
            config = NodeConfig(
                order_id=1,
                server_address=test_server_address,
                server_port=443,
                client_email=f'test_delete_{i}_{uuid.uuid4().hex[:8]}@example.com',
                client_id=str(uuid.uuid4()),
                vless_config=f'vless://test-delete-config-{i}',
                is_active=True
            )
            db.session.add(config)
        
        db.session.commit()
        
        configs_before_delete = NodeConfig.query.filter(
            NodeConfig.server_address == test_server_address,
            NodeConfig.is_active == True
        ).count()
        
        print(f'   删除前活跃节点配置: {configs_before_delete}')
        
        try:
            # 模拟完全删除面板（包含自动清理逻辑）
            orphaned_configs = NodeConfig.query.filter(
                NodeConfig.server_address == test_server_address,
                NodeConfig.is_active == True
            ).all()
            
            cleaned_configs = 0
            for config in orphaned_configs:
                config.is_active = False
                cleaned_configs += 1
            
            # 清理分组成员关系
            orphaned_memberships = XUIPanelGroupMembership.query.filter_by(panel_id=test_panel.id).all()
            cleaned_memberships = 0
            for membership in orphaned_memberships:
                db.session.delete(membership)
                cleaned_memberships += 1
            
            # 删除面板
            db.session.delete(test_panel)
            db.session.commit()
            
            print(f'   ✓ 面板删除成功，清理了 {cleaned_configs} 个节点配置和 {cleaned_memberships} 个分组关系')
            
        except Exception as e:
            print(f'   ❌ 面板删除失败: {e}')
            db.session.rollback()
        
        # 9. 最终验证
        print(f'\n9. 最终验证:')
        
        final_configs = NodeConfig.query.filter(
            NodeConfig.server_address == test_server_address,
            NodeConfig.is_active == True
        ).count()
        
        final_panel = XUIPanel.query.get(test_panel.id)
        
        print(f'   最终活跃节点配置: {final_configs}')
        print(f'   测试面板是否存在: {"否" if final_panel is None else "是"}')
        
        if final_configs == 0 and final_panel is None:
            print('   ✅ 自动清理功能完全正常！')
        else:
            print('   ⚠ 自动清理功能可能存在问题')
        
        print(f'\n=== 自动数据清理功能测试完成 ===')

if __name__ == '__main__':
    test_auto_cleanup()
