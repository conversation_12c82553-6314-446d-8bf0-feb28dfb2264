#!/usr/bin/env python3
"""
数据清理服务 - 处理删除操作后的数据库遗留问题
"""
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Tuple
from sqlalchemy import and_, or_

from models import db, XUIPanel, XUIPanelGroup, XUIPanelGroupMembership, NodeConfig, TrafficStats, Subscription, Order

logger = logging.getLogger(__name__)

class DataCleanupService:
    """数据清理服务类"""
    
    def __init__(self):
        self.logger = logger
    
    def cleanup_orphaned_node_configs(self) -> Dict:
        """
        清理孤儿节点配置
        - 删除引用不存在面板的NodeConfig记录
        """
        try:
            self.logger.info("开始清理孤儿节点配置...")
            
            # 获取所有活跃的面板服务器地址
            active_panels = XUIPanel.query.filter_by(status='active').all()
            active_addresses = set()
            for panel in active_panels:
                server_address = panel.base_url.replace('http://', '').replace('https://', '').split(':')[0]
                active_addresses.add(server_address)
            
            self.logger.info(f"找到 {len(active_addresses)} 个活跃面板地址")
            
            # 查找引用不存在面板的NodeConfig
            orphaned_configs = []
            all_node_configs = NodeConfig.query.filter_by(is_active=True).all()
            
            for config in all_node_configs:
                if config.server_address not in active_addresses:
                    orphaned_configs.append(config)
            
            if not orphaned_configs:
                return {
                    'success': True,
                    'cleaned_count': 0,
                    'message': '没有发现孤儿节点配置'
                }
            
            self.logger.info(f"发现 {len(orphaned_configs)} 个孤儿节点配置")
            
            # 记录详细信息
            cleanup_details = []
            for config in orphaned_configs:
                detail = {
                    'config_id': config.id,
                    'order_id': config.order_id,
                    'server_address': config.server_address,
                    'client_email': config.client_email
                }
                cleanup_details.append(detail)
                
                # 停用而不是删除，保留数据完整性
                config.is_active = False
                self.logger.info(f"停用孤儿节点配置: {config.id} (服务器: {config.server_address})")
            
            db.session.commit()
            
            return {
                'success': True,
                'cleaned_count': len(orphaned_configs),
                'message': f'成功清理 {len(orphaned_configs)} 个孤儿节点配置',
                'details': cleanup_details
            }
            
        except Exception as e:
            db.session.rollback()
            self.logger.error(f"清理孤儿节点配置失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def cleanup_orphaned_traffic_stats(self) -> Dict:
        """
        清理孤儿流量统计数据
        - 删除引用不存在订阅的流量统计记录
        """
        try:
            self.logger.info("开始清理孤儿流量统计数据...")
            
            # 获取所有活跃订阅ID
            active_subscription_ids = set(
                sub.id for sub in Subscription.query.filter_by(is_active=True).all()
            )
            
            self.logger.info(f"找到 {len(active_subscription_ids)} 个活跃订阅")
            
            # 查找引用不存在订阅的流量统计
            orphaned_stats = TrafficStats.query.filter(
                ~TrafficStats.subscription_id.in_(active_subscription_ids)
            ).all()
            
            if not orphaned_stats:
                return {
                    'success': True,
                    'cleaned_count': 0,
                    'message': '没有发现孤儿流量统计数据'
                }
            
            self.logger.info(f"发现 {len(orphaned_stats)} 条孤儿流量统计记录")
            
            # 记录清理详情
            cleanup_details = []
            total_traffic_mb = 0
            
            for stat in orphaned_stats:
                detail = {
                    'stat_id': stat.id,
                    'subscription_id': stat.subscription_id,
                    'recorded_at': stat.recorded_at.isoformat() if stat.recorded_at else None,
                    'up_mb': stat.upload_bytes / (1024**2) if stat.upload_bytes else 0,
                    'down_mb': stat.download_bytes / (1024**2) if stat.download_bytes else 0
                }
                cleanup_details.append(detail)
                total_traffic_mb += detail['up_mb'] + detail['down_mb']
                
                db.session.delete(stat)
            
            db.session.commit()
            
            return {
                'success': True,
                'cleaned_count': len(orphaned_stats),
                'total_traffic_mb_cleaned': round(total_traffic_mb, 2),
                'message': f'成功清理 {len(orphaned_stats)} 条孤儿流量统计记录，释放 {total_traffic_mb:.2f} MB 流量数据',
                'details': cleanup_details[:10]  # 只返回前10条详情
            }
            
        except Exception as e:
            db.session.rollback()
            self.logger.error(f"清理孤儿流量统计数据失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def cleanup_panel_removal_aftermath(self, panel_id: int) -> Dict:
        """
        清理面板删除后的遗留数据
        
        Args:
            panel_id: 被删除的面板ID
        """
        try:
            self.logger.info(f"开始清理面板 {panel_id} 删除后的遗留数据...")
            
            # 获取面板信息（如果还存在）
            panel = XUIPanel.query.get(panel_id)
            if panel:
                server_address = panel.base_url.replace('http://', '').replace('https://', '').split(':')[0]
            else:
                # 面板已删除，通过NodeConfig推断服务器地址
                sample_config = NodeConfig.query.filter_by(is_active=True).first()
                if not sample_config:
                    return {
                        'success': True,
                        'message': '没有找到相关的节点配置'
                    }
                server_address = None
            
            cleanup_results = {
                'node_configs_cleaned': 0,
                'traffic_stats_cleaned': 0,
                'group_memberships_cleaned': 0
            }
            
            # 1. 清理节点配置
            if server_address:
                orphaned_configs = NodeConfig.query.filter(
                    and_(
                        NodeConfig.server_address == server_address,
                        NodeConfig.is_active == True
                    )
                ).all()
                
                for config in orphaned_configs:
                    config.is_active = False
                    cleanup_results['node_configs_cleaned'] += 1
                    self.logger.info(f"停用节点配置: {config.id}")
            
            # 2. 清理分组成员关系
            orphaned_memberships = XUIPanelGroupMembership.query.filter_by(panel_id=panel_id).all()
            for membership in orphaned_memberships:
                db.session.delete(membership)
                cleanup_results['group_memberships_cleaned'] += 1
                self.logger.info(f"删除分组成员关系: 面板 {panel_id} -> 分组 {membership.group_id}")
            
            db.session.commit()
            
            return {
                'success': True,
                'message': f'面板 {panel_id} 遗留数据清理完成',
                'details': cleanup_results
            }
            
        except Exception as e:
            db.session.rollback()
            self.logger.error(f"清理面板 {panel_id} 遗留数据失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def comprehensive_cleanup(self) -> Dict:
        """
        综合数据清理
        - 执行所有清理操作
        """
        try:
            self.logger.info("开始综合数据清理...")
            
            results = {
                'orphaned_node_configs': self.cleanup_orphaned_node_configs(),
                'orphaned_traffic_stats': self.cleanup_orphaned_traffic_stats(),
                'total_cleaned_items': 0,
                'total_traffic_mb_cleaned': 0
            }
            
            # 统计总清理数量
            for result in results.values():
                if isinstance(result, dict) and result.get('success'):
                    results['total_cleaned_items'] += result.get('cleaned_count', 0)
                    results['total_traffic_mb_cleaned'] += result.get('total_traffic_mb_cleaned', 0)
            
            self.logger.info(f"综合数据清理完成，共清理 {results['total_cleaned_items']} 项数据")
            
            return {
                'success': True,
                'message': f'综合数据清理完成，共清理 {results["total_cleaned_items"]} 项数据',
                'results': results
            }
            
        except Exception as e:
            self.logger.error(f"综合数据清理失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_cleanup_report(self) -> Dict:
        """
        生成数据清理报告
        """
        try:
            # 统计各种数据
            total_node_configs = NodeConfig.query.count()
            active_node_configs = NodeConfig.query.filter_by(is_active=True).count()
            inactive_node_configs = total_node_configs - active_node_configs
            
            total_traffic_stats = TrafficStats.query.count()
            active_subscriptions = Subscription.query.filter_by(is_active=True).count()
            
            total_panels = XUIPanel.query.count()
            active_panels = XUIPanel.query.filter_by(status='active').count()
            
            # 检查潜在的孤儿数据
            active_panel_addresses = set()
            for panel in XUIPanel.query.filter_by(status='active').all():
                server_address = panel.base_url.replace('http://', '').replace('https://', '').split(':')[0]
                active_panel_addresses.add(server_address)
            
            potential_orphaned_configs = 0
            for config in NodeConfig.query.filter_by(is_active=True).all():
                if config.server_address not in active_panel_addresses:
                    potential_orphaned_configs += 1
            
            active_subscription_ids = set(sub.id for sub in Subscription.query.filter_by(is_active=True).all())
            potential_orphaned_stats = TrafficStats.query.filter(
                ~TrafficStats.subscription_id.in_(active_subscription_ids)
            ).count()
            
            return {
                'success': True,
                'report': {
                    'node_configs': {
                        'total': total_node_configs,
                        'active': active_node_configs,
                        'inactive': inactive_node_configs,
                        'potential_orphaned': potential_orphaned_configs
                    },
                    'traffic_stats': {
                        'total': total_traffic_stats,
                        'potential_orphaned': potential_orphaned_stats
                    },
                    'panels': {
                        'total': total_panels,
                        'active': active_panels
                    },
                    'subscriptions': {
                        'active': active_subscriptions
                    }
                },
                'recommendations': self._get_cleanup_recommendations(
                    potential_orphaned_configs, potential_orphaned_stats
                )
            }
            
        except Exception as e:
            self.logger.error(f"生成清理报告失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _get_cleanup_recommendations(self, orphaned_configs: int, orphaned_stats: int) -> List[str]:
        """生成清理建议"""
        recommendations = []
        
        if orphaned_configs > 0:
            recommendations.append(f"发现 {orphaned_configs} 个可能的孤儿节点配置，建议执行节点配置清理")
        
        if orphaned_stats > 0:
            recommendations.append(f"发现 {orphaned_stats} 条可能的孤儿流量统计记录，建议执行流量统计清理")
        
        if not recommendations:
            recommendations.append("数据状态良好，暂无需要清理的项目")
        
        return recommendations

# 创建全局实例
data_cleanup_service = DataCleanupService()
