#!/usr/bin/env python3
"""
分析流量数据收集机制
解释为什么调度器每次执行都会新增一条数据
"""
from app import create_app
from models import db, TrafficStats, Subscription, Order
from services.scheduler_service import scheduler_service
from datetime import datetime, timedelta
import uuid

def analyze_traffic_collection_mechanism():
    """分析流量数据收集机制"""
    app = create_app()
    with app.app_context():
        print('=== 流量数据收集机制分析 ===\n')
        
        # 1. 调度器配置分析
        print('1. 调度器配置分析:')
        
        print('   流量统计任务配置:')
        print('   - 执行频率: 每5分钟执行一次')
        print('   - 任务ID: traffic_stats_collection')
        print('   - 触发器: IntervalTrigger(minutes=5)')
        print('   - 最大实例: 1 (确保只有一个实例运行)')
        print('   - 合并任务: True (合并错过的任务)')
        
        # 2. 流量收集逻辑分析
        print(f'\n2. 流量收集逻辑分析:')
        
        print('   收集流程:')
        print('   ① 调度器每5分钟触发一次 _collect_traffic_stats_job')
        print('   ② 调用 traffic_stats_service.collect_all_traffic_stats()')
        print('   ③ 获取所有活跃订阅')
        print('   ④ 按分组批量处理订阅')
        print('   ⑤ 为每个订阅创建一条新的流量统计记录')
        print('   ⑥ 保存到数据库')
        
        # 3. 为什么每次都新增数据
        print(f'\n3. 为什么每次执行都新增一条数据:')
        
        print('   📊 设计原理:')
        print('   - 流量统计采用"时间序列"设计')
        print('   - 每次收集都是一个"快照"，记录当前时刻的累计流量')
        print('   - 不是增量记录，而是全量记录')
        print('   - 每条记录都有时间戳 (recorded_at)')
        
        print(f'\n   🔄 数据结构:')
        print('   TrafficStats表结构:')
        print('   - id: 主键')
        print('   - subscription_id: 订阅ID')
        print('   - upload_bytes: 上传字节数（累计）')
        print('   - download_bytes: 下载字节数（累计）')
        print('   - total_bytes: 总字节数（累计）')
        print('   - recorded_at: 记录时间')
        
        # 4. 实际数据验证
        print(f'\n4. 实际数据验证:')
        
        # 获取最近的流量记录
        recent_stats = TrafficStats.query.order_by(
            TrafficStats.recorded_at.desc()
        ).limit(5).all()
        
        if recent_stats:
            print(f'   最近5条流量记录:')
            for i, stat in enumerate(recent_stats, 1):
                print(f'   {i}. 订阅{stat.subscription_id}: {stat.total_bytes}字节 '
                      f'@ {stat.recorded_at}')
        else:
            print('   暂无流量记录')
        
        # 5. 时间间隔分析
        if len(recent_stats) >= 2:
            print(f'\n5. 时间间隔分析:')
            
            for i in range(len(recent_stats) - 1):
                current = recent_stats[i]
                next_record = recent_stats[i + 1]
                
                time_diff = current.recorded_at - next_record.recorded_at
                minutes_diff = time_diff.total_seconds() / 60
                
                print(f'   记录{i+1} -> 记录{i+2}: {minutes_diff:.1f}分钟间隔')
        
        # 6. 数据增长模式分析
        print(f'\n6. 数据增长模式分析:')
        
        # 按订阅统计记录数
        subscription_stats = db.session.query(
            TrafficStats.subscription_id,
            db.func.count(TrafficStats.id).label('record_count'),
            db.func.min(TrafficStats.recorded_at).label('first_record'),
            db.func.max(TrafficStats.recorded_at).label('last_record')
        ).group_by(TrafficStats.subscription_id).all()
        
        print(f'   各订阅的记录统计:')
        for stat in subscription_stats:
            sub_id, count, first, last = stat
            if first and last:
                duration = last - first
                hours = duration.total_seconds() / 3600
                expected_records = int(hours / (5/60))  # 每5分钟一条
                
                print(f'   订阅{sub_id}: {count}条记录, 时间跨度{hours:.1f}小时, '
                      f'预期{expected_records}条')
        
        # 7. 为什么这样设计
        print(f'\n7. 为什么这样设计:')
        
        print('   ✅ 优点:')
        print('   - 完整的历史记录: 可以查看任意时间点的流量状态')
        print('   - 时间序列分析: 支持流量趋势分析和图表展示')
        print('   - 数据完整性: 即使某次收集失败，不影响其他时间点')
        print('   - 简单可靠: 每次都是独立的全量记录，逻辑简单')
        print('   - 支持回溯: 可以查看历史任意时间的流量使用情况')
        
        print(f'\n   ⚠️ 缺点:')
        print('   - 数据量大: 每5分钟一条记录，长期累积数据量大')
        print('   - 存储成本: 需要更多存储空间')
        print('   - 查询性能: 数据量大时查询可能较慢')
        
        # 8. 替代方案分析
        print(f'\n8. 替代方案分析:')
        
        print('   🔄 方案1: 增量记录')
        print('   - 只记录每次的流量增量')
        print('   - 优点: 数据量小')
        print('   - 缺点: 需要复杂的累计计算，容易出错')
        
        print(f'\n   🔄 方案2: 覆盖更新')
        print('   - 每次更新同一条记录')
        print('   - 优点: 数据量最小')
        print('   - 缺点: 丢失历史数据，无法分析趋势')
        
        print(f'\n   🔄 方案3: 分层存储（当前优化方案）')
        print('   - 保留详细记录，定期清理旧数据')
        print('   - 优点: 平衡了历史数据和存储成本')
        print('   - 缺点: 需要额外的清理机制')
        
        # 9. 当前优化效果
        print(f'\n9. 当前优化效果:')
        
        total_records = TrafficStats.query.count()
        retention_days = app.config.get('TRAFFIC_DATA_RETENTION_DAYS', 30)
        
        print(f'   当前记录数: {total_records}')
        print(f'   保留天数: {retention_days}天')
        print(f'   理论上限: {retention_days * 288}条记录')
        
        if total_records > retention_days * 288:
            print(f'   ⚠️ 当前记录数超过理论上限，建议执行清理')
        else:
            print(f'   ✅ 当前记录数在合理范围内')
        
        # 10. 总结
        print(f'\n10. 总结:')
        
        print(f'   📋 调度器每次执行都新增数据的原因:')
        print(f'   1. 设计目的: 创建时间序列流量数据')
        print(f'   2. 记录性质: 每条记录是一个时间点的流量快照')
        print(f'   3. 业务需求: 支持流量历史查询和趋势分析')
        print(f'   4. 技术实现: 简单可靠的全量记录方式')
        
        print(f'\n   🎯 这是正常且必要的行为:')
        print(f'   - 不是bug，而是功能特性')
        print(f'   - 支持流量监控和计费')
        print(f'   - 提供完整的使用历史')
        print(f'   - 已通过优化方案控制数据量')
        
        print(f'\n   💡 如果担心数据量:')
        print(f'   - 已实施流量数据优化方案')
        print(f'   - 自动清理超过{retention_days}天的数据')
        print(f'   - 数据量控制在合理范围内')
        print(f'   - 可通过配置调整保留期')
        
        print(f'\n=== 流量数据收集机制分析完成 ===')

if __name__ == '__main__':
    analyze_traffic_collection_mechanism()
