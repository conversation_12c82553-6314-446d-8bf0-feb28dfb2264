#!/usr/bin/env python3
"""
自定义协议类型功能测试脚本
"""
from app import create_app
from models import db, ProtocolTemplate, NodeType
from services.protocol_template_service import ProtocolTemplateService

def main():
    app = create_app()
    with app.app_context():
        print('=== 自定义协议类型功能测试 ===\n')
        
        # 1. 测试自定义协议模板
        custom_template = ProtocolTemplate.query.filter_by(protocol_type=NodeType.CUSTOM).first()
        if custom_template:
            print(f'1. 自定义协议模板:')
            print(f'   名称: {custom_template.name}')
            print(f'   协议类型: {custom_template.protocol_type.value}')
            print(f'   自定义协议名称: {custom_template.custom_protocol_name}')
            print(f'   显示名称: {custom_template.protocol_type_display}')
            print(f'   模板内容: {custom_template.template_content}')
            
            # 2. 测试模板验证
            print(f'\n2. 模板验证测试:')
            is_valid, missing = custom_template.validate_template()
            result = "通过" if is_valid else "失败"
            print(f'   验证结果: {result}')
            if missing:
                print(f'   缺失占位符: {missing}')
            
            # 3. 测试配置生成
            print(f'\n3. 配置生成测试:')
            service = ProtocolTemplateService()
            
            sample_variables = {
                'client_id': 'test-client-id',
                'server_address': 'hysteria.example.com',
                'server_port': 36712,
                'client_email': '<EMAIL>'
            }
            
            config = service.parse_template(custom_template.template_content, sample_variables)
            print(f'   生成的配置: {config}')
        
        # 4. 测试所有协议类型
        print(f'\n4. 所有协议类型统计:')
        for protocol_type in NodeType:
            count = ProtocolTemplate.query.filter_by(protocol_type=protocol_type).count()
            print(f'   {protocol_type.value.upper()}: {count} 个模板')
        
        # 5. 测试删除默认模板功能
        print(f'\n5. 默认模板删除测试:')
        default_templates = ProtocolTemplate.query.filter_by(is_default=True).all()
        print(f'   默认模板数量: {len(default_templates)}')
        print(f'   现在管理员可以删除默认模板')
        
        print(f'\n=== 测试完成 ===')

if __name__ == '__main__':
    main()
