"""
订阅同步服务 - 处理分组面板变更时的订阅节点同步
"""
import logging
import uuid
from typing import List, Dict, Optional, Tuple
from datetime import datetime
from sqlalchemy import and_, or_
from models import db, XUIPanelGroup, XUIPanel, Subscription, Order, NodeConfig, OrderStatus, XUIPanelGroupMembership
from models.enums import PanelStatus
from multi_xui_manager import MultiXUIManager
from xui_client import XUIClient

logger = logging.getLogger(__name__)

class SubscriptionSyncService:
    """订阅同步服务类 - 使用批量添加客户端功能"""

    def __init__(self):
        self.xui_manager = None
    
    def sync_group_subscriptions_for_new_panel(self, group_id: int, panel_id: int) -> Dict:
        """
        为分组中新增的面板批量同步所有活跃订阅（使用批量添加客户端功能）

        Args:
            group_id: 分组ID
            panel_id: 新增的面板ID

        Returns:
            同步结果统计
        """
        try:
            # 获取分组和面板信息
            group = XUIPanelGroup.query.get(group_id)
            panel = XUIPanel.query.get(panel_id)

            if not group or not panel:
                error_msg = f"分组或面板不存在: group_id={group_id}, panel_id={panel_id}"
                return {'success': False, 'error': error_msg}

            # 获取该分组的所有活跃订阅
            active_subscriptions = self._get_group_active_subscriptions(group_id)

            if not active_subscriptions:
                return {
                    'success': True,
                    'affected_subscriptions': 0,
                    'success_count': 0,
                    'failed_count': 0,
                    'message': '该分组没有活跃订阅'
                }

            logger.info(f"开始为面板 {panel.name} 批量同步 {len(active_subscriptions)} 个订阅")

            # 使用批量同步方法
            result = self._batch_sync_subscriptions_to_panel(active_subscriptions, panel)

            # 提交数据库事务
            if result['success']:
                db.session.commit()
                logger.info(f"分组 {group.name} 新增面板 {panel.name} 的批量同步完成: "
                           f"成功 {result['success_count']}, 失败 {result['failed_count']}")
            else:
                db.session.rollback()
                logger.error(f"分组 {group.name} 新增面板 {panel.name} 的批量同步失败: {result.get('error', '未知错误')}")

            return {
                'success': result['success'],
                'affected_subscriptions': len(active_subscriptions),
                'success_count': result['success_count'],
                'failed_count': result['failed_count'],
                'error': result.get('error'),
                'panel_results': result.get('panel_results', {})
            }

        except Exception as e:
            db.session.rollback()
            error_msg = f"同步过程发生错误: {str(e)}"
            logger.error(error_msg)
            return {'success': False, 'error': error_msg}
    
    def _get_group_active_subscriptions(self, group_id: int) -> List:
        """获取分组的所有活跃订阅"""
        try:
            # {{CHENGQI:
            # Action: Modified
            # Timestamp: 2025-06-09 20:50:00 +08:00
            # Task_ID: P4-LD-002
            # Principle_Applied: 修复外键歧义问题，明确指定JOIN条件
            # Language: Python
            # Description: 修复Subscription和Order之间的JOIN歧义，明确使用order_id关系
            # }}

            # 查询该分组的所有活跃订阅，明确指定JOIN条件避免歧义
            subscriptions = db.session.query(Subscription).join(
                Order, Subscription.order_id == Order.id
            ).filter(
                and_(
                    Subscription.group_id == group_id,
                    Subscription.is_active == True,
                    Order.status == OrderStatus.COMPLETED,
                    or_(
                        Order.expires_at.is_(None),
                        Order.expires_at > datetime.utcnow()
                    )
                )
            ).all()

            return subscriptions

        except Exception as e:
            logger.error(f"获取分组 {group_id} 的活跃订阅失败: {e}")
            return []

    def _batch_sync_subscriptions_to_panel(self, subscriptions: List, panel: XUIPanel) -> Dict:
        """
        批量同步订阅到指定面板（使用批量添加客户端功能）

        Args:
            subscriptions: 订阅列表
            panel: 目标面板

        Returns:
            Dict: 同步结果
        """
        try:
            if not subscriptions:
                return {
                    'success': True,
                    'success_count': 0,
                    'failed_count': 0,
                    'message': '没有订阅需要同步'
                }

            # 创建X-UI客户端
            xui_client = XUIClient(
                base_url=panel.base_url,
                username=panel.username,
                password=panel.password,
                path_prefix=panel.path_prefix
            )

            # 获取面板的入站配置
            inbounds = xui_client.get_inbounds()
            if not inbounds:
                return {
                    'success': False,
                    'error': f'面板 {panel.name} 无法获取入站规则',
                    'success_count': 0,
                    'failed_count': len(subscriptions)
                }

            # 获取面板在分组中的成员关系，确定入站ID
            membership = XUIPanelGroupMembership.query.filter_by(panel_id=panel.id).first()
            target_inbound_id = membership.inbound_id if membership and membership.inbound_id else 1

            # 查找目标入站配置
            target_inbound = None
            for inbound in inbounds:
                if inbound.get('id') == target_inbound_id:
                    target_inbound = inbound
                    break

            if not target_inbound:
                # 如果指定的入站不存在，使用第一个可用的入站
                target_inbound = inbounds[0]
                target_inbound_id = target_inbound.get('id')
                logger.warning(f"面板 {panel.name} 指定的入站 {target_inbound_id} 不存在，使用入站 {target_inbound_id}")

            logger.info(f"面板 {panel.name} 使用入站 {target_inbound_id} 进行批量同步")

            # 准备批量客户端数据
            clients_to_add = []
            subscription_mapping = {}  # 用于记录客户端和订阅的对应关系

            for subscription in subscriptions:
                try:
                    order = subscription.order
                    if not order or order.status != OrderStatus.COMPLETED:
                        logger.warning(f"订阅 {subscription.id} 的订单状态无效，跳过")
                        continue

                    # 检查是否已经在该面板中有节点
                    server_address = panel.base_url.replace('http://', '').replace('https://', '').split(':')[0]
                    existing_node = NodeConfig.query.filter(
                        and_(
                            NodeConfig.order_id == order.id,
                            NodeConfig.server_address == server_address
                        )
                    ).first()

                    if existing_node:
                        logger.info(f"订阅 {subscription.id} 在面板 {panel.name} 已有节点，跳过")
                        continue

                    # 生成客户端信息
                    client_id = str(uuid.uuid4())
                    client_email = f"{order.customer_email}_{order.order_id}"

                    # 检查XUI面板中是否已存在该客户端
                    if xui_client.client_exists_by_email(client_email):
                        logger.info(f"客户端 {client_email} 已存在于面板 {panel.name}，跳过")
                        continue

                    # 计算流量限制（转换为字节）
                    traffic_limit_bytes = order.traffic_limit_gb * 1024 * 1024 * 1024

                    # 计算过期时间（转换为毫秒时间戳）
                    expiry_time = 0
                    if order.expires_at:
                        expiry_time = int(order.expires_at.timestamp() * 1000)

                    # 准备客户端配置
                    client_config = {
                        "id": client_id,
                        "email": client_email,
                        "totalGB": traffic_limit_bytes,
                        "expiryTime": expiry_time,
                        "limitIp": 0,
                        "enable": True,
                        "flow": "",
                        "tgId": "",
                        "subId": subscription.subscription_token[:16],
                        "comment": f"订单:{order.order_id}",
                        "reset": 0,
                        "security": "auto"
                    }

                    clients_to_add.append(client_config)
                    subscription_mapping[client_email] = {
                        'subscription': subscription,
                        'order': order,
                        'client_id': client_id,
                        'client_config': client_config
                    }

                except Exception as e:
                    logger.error(f"准备订阅 {subscription.id} 的客户端数据时发生错误: {e}")
                    continue

            if not clients_to_add:
                return {
                    'success': True,
                    'success_count': 0,
                    'failed_count': 0,
                    'message': '没有新的客户端需要添加'
                }

            logger.info(f"准备向面板 {panel.name} 批量添加 {len(clients_to_add)} 个客户端")

            # 执行批量添加
            success, response = xui_client.add_clients_batch(target_inbound_id, clients_to_add)

            if success:
                # 批量添加成功，创建NodeConfig记录
                success_count = 0
                failed_count = 0

                for client_config in clients_to_add:
                    try:
                        client_email = client_config['email']
                        mapping = subscription_mapping[client_email]
                        subscription = mapping['subscription']
                        order = mapping['order']
                        client_id = mapping['client_id']

                        # 生成协议配置
                        config_string = self._generate_protocol_config(
                            panel, target_inbound, client_config, client_email
                        )

                        # 创建NodeConfig记录
                        node_config = NodeConfig(
                            order_id=order.id,
                            xui_inbound_id=target_inbound_id,
                            client_id=client_id,
                            client_email=client_email,
                            server_address=panel.base_url.replace('http://', '').replace('https://', '').split(':')[0],
                            server_port=target_inbound.get('port', 443),
                            protocol=order.node_type.value,
                            vless_config=config_string
                        )

                        db.session.add(node_config)
                        success_count += 1

                    except Exception as e:
                        logger.error(f"创建NodeConfig记录失败: {e}")
                        failed_count += 1

                logger.info(f"面板 {panel.name} 批量同步完成: 成功 {success_count}, 失败 {failed_count}")

                return {
                    'success': True,
                    'success_count': success_count,
                    'failed_count': failed_count,
                    'message': f'批量添加 {len(clients_to_add)} 个客户端成功'
                }

            else:
                error_msg = response.get('error', '批量添加失败')
                logger.error(f"面板 {panel.name} 批量添加客户端失败: {error_msg}")
                return {
                    'success': False,
                    'success_count': 0,
                    'failed_count': len(clients_to_add),
                    'error': error_msg
                }

        except Exception as e:
            logger.error(f"批量同步到面板 {panel.name} 时发生错误: {e}")
            return {
                'success': False,
                'success_count': 0,
                'failed_count': len(subscriptions),
                'error': str(e)
            }
    
    # 注意：原来的 _add_panel_node_for_subscription 方法已被移除
    # 现在统一使用 _batch_sync_subscriptions_to_panel 方法进行批量同步
    


    def sync_group_all_panels(self, group_id: int) -> Dict:
        """
        同步分组中所有面板的订阅（使用批量添加客户端功能）
        这是为手动同步功能专门设计的方法

        Args:
            group_id: 分组ID

        Returns:
            同步结果统计
        """
        try:
            # 获取分组信息
            group = XUIPanelGroup.query.get(group_id)
            if not group:
                return {'success': False, 'error': f'分组不存在: group_id={group_id}'}

            # 获取分组中的所有面板
            panels = group.panels
            if not panels:
                return {
                    'success': True,
                    'message': '分组中没有面板',
                    'affected_subscriptions': 0,
                    'success_count': 0,
                    'failed_count': 0
                }

            # 获取该分组的所有活跃订阅
            active_subscriptions = self._get_group_active_subscriptions(group_id)
            if not active_subscriptions:
                return {
                    'success': True,
                    'message': '该分组没有活跃订阅',
                    'affected_subscriptions': 0,
                    'success_count': 0,
                    'failed_count': 0
                }

            logger.info(f"开始批量同步分组 {group.name}: {len(active_subscriptions)} 个订阅, {len(panels)} 个面板")

            # 统计结果
            total_success = 0
            total_failed = 0
            panel_results = []

            # 为每个面板执行批量同步
            for panel in panels:
                try:
                    logger.info(f"开始批量同步面板 {panel.name}")

                    # 使用批量同步方法
                    result = self._batch_sync_subscriptions_to_panel(active_subscriptions, panel)

                    panel_success = result['success_count']
                    panel_failed = result['failed_count']

                    total_success += panel_success
                    total_failed += panel_failed

                    panel_results.append({
                        'panel_name': panel.name,
                        'panel_id': panel.id,
                        'success_count': panel_success,
                        'failed_count': panel_failed,
                        'success': result['success'],
                        'message': result.get('message', ''),
                        'error': result.get('error')
                    })

                    logger.info(f"面板 {panel.name} 批量同步完成: 成功 {panel_success}, 失败 {panel_failed}")

                except Exception as e:
                    logger.error(f"面板 {panel.name} 批量同步过程异常: {e}")
                    panel_results.append({
                        'panel_name': panel.name,
                        'panel_id': panel.id,
                        'success_count': 0,
                        'failed_count': len(active_subscriptions),
                        'success': False,
                        'error': str(e)
                    })
                    total_failed += len(active_subscriptions)

            # 提交数据库事务
            db.session.commit()

            logger.info(f"分组 {group.name} 全面板批量同步完成: 总成功 {total_success}, 总失败 {total_failed}")

            return {
                'success': True,
                'group_name': group.name,
                'affected_subscriptions': len(active_subscriptions),
                'panel_count': len(panels),
                'success_count': total_success,
                'failed_count': total_failed,
                'panel_results': panel_results
            }

        except Exception as e:
            db.session.rollback()
            error_msg = f"全面板批量同步过程发生错误: {str(e)}"
            logger.error(error_msg)
            return {'success': False, 'error': error_msg}

    def _generate_protocol_config(self, panel, inbound_config, client_data, client_email):
        """生成协议配置，支持多协议模板"""
        try:
            # 检查面板是否有关联的协议模板
            from models import XUIPanelGroupMembership, ProtocolTemplate

            # 查找面板在分组中的成员关系，看是否指定了协议模板
            membership = XUIPanelGroupMembership.query.filter_by(panel_id=panel.id).first()

            if membership and membership.protocol_template_id:
                # 使用协议模板生成配置
                template = ProtocolTemplate.query.get(membership.protocol_template_id)
                if template and template.is_active:
                    config = self._generate_config_from_template(
                        template, panel, inbound_config, client_data
                    )
                    if config:
                        return config

            # 回退到默认VLESS配置生成
            from xui_client import XUIClient
            xui_client = XUIClient(
                base_url=panel.base_url,
                username=panel.username,
                password=panel.password,
                path_prefix=panel.path_prefix
            )

            return xui_client.generate_vless_config(
                inbound_config, client_data.get('id'), client_email
            )

        except Exception as e:
            logger.error(f"生成协议配置失败: {e}")
            # 回退到默认VLESS配置
            from xui_client import XUIClient
            xui_client = XUIClient(
                base_url=panel.base_url,
                username=panel.username,
                password=panel.password,
                path_prefix=panel.path_prefix
            )

            return xui_client.generate_vless_config(
                inbound_config, client_data.get('id'), client_email
            )

    def _generate_config_from_template(self, template, panel, inbound_config, client_data):
        """使用协议模板生成配置"""
        try:
            from services.protocol_template_service import ProtocolTemplateService

            # 准备面板配置信息
            panel_config = {
                'server_address': panel.base_url.replace('http://', '').replace('https://', '').split(':')[0],
                'base_url': panel.base_url,
                'name': panel.name
            }

            # 使用协议模板服务生成配置
            template_service = ProtocolTemplateService()
            config = template_service.generate_config(
                template.id, inbound_config, panel_config, client_data
            )

            if config:
                logger.info(f"使用协议模板 {template.name} 生成配置成功")
                return config
            else:
                logger.warning(f"协议模板 {template.name} 生成配置失败，回退到默认方式")
                return None

        except Exception as e:
            logger.error(f"使用协议模板生成配置失败: {e}")
            return None

    def sync_subscriptions_batch(self, subscription_ids: List[int] = None, group_id: int = None) -> Dict:
        """
        批量同步订阅到X-UI面板

        Args:
            subscription_ids: 指定要同步的订阅ID列表，如果为None则同步所有活跃订阅
            group_id: 指定要同步到的分组ID，如果为None则使用订阅的默认分组

        Returns:
            Dict: 同步结果统计
        """
        try:
            logger.info("开始批量同步订阅...")

            # 获取要同步的订阅
            if subscription_ids:
                subscriptions = Subscription.query.filter(
                    Subscription.id.in_(subscription_ids),
                    Subscription.is_active == True
                ).all()
            else:
                subscriptions = Subscription.query.filter_by(is_active=True).all()

            if not subscriptions:
                logger.warning("没有找到需要同步的订阅")
                return {
                    'total_subscriptions': 0,
                    'success_count': 0,
                    'failed_count': 0,
                    'panel_results': {}
                }

            logger.info(f"找到 {len(subscriptions)} 个订阅需要同步")

            # 按分组和面板组织订阅
            panel_clients = {}  # {panel_id: {inbound_id: [clients]}}

            for subscription in subscriptions:
                try:
                    order = subscription.order
                    if not order or order.status != OrderStatus.COMPLETED:
                        logger.warning(f"订阅 {subscription.id} 的订单状态无效，跳过")
                        continue

                    # 确定目标分组
                    target_group_id = group_id or subscription.group_id
                    if not target_group_id:
                        logger.warning(f"订阅 {subscription.id} 没有指定分组，跳过")
                        continue

                    # 获取分组中的面板
                    group = XUIPanelGroup.query.get(target_group_id)
                    if not group:
                        logger.warning(f"分组 {target_group_id} 不存在，跳过订阅 {subscription.id}")
                        continue

                    # 为每个面板准备客户端数据
                    for membership in group.memberships:
                        panel = membership.panel
                        if not panel or panel.status != PanelStatus.ACTIVE:
                            continue

                        inbound_id = membership.inbound_id or 1

                        # 生成客户端配置
                        client_id = str(uuid.uuid4())
                        client_email = f"{order.customer_email}_{order.order_id}"

                        # 计算流量限制（转换为字节）
                        traffic_limit_bytes = order.traffic_limit_gb * 1024 * 1024 * 1024

                        # 计算过期时间（转换为毫秒时间戳）
                        expiry_time = 0
                        if order.expires_at:
                            expiry_time = int(order.expires_at.timestamp() * 1000)

                        client_config = {
                            "id": client_id,
                            "email": client_email,
                            "totalGB": traffic_limit_bytes,
                            "expiryTime": expiry_time,
                            "limitIp": 0,
                            "enable": True,
                            "flow": "",
                            "tgId": "",
                            "subId": subscription.subscription_token[:16],  # 使用订阅令牌的前16位作为subId
                            "comment": f"订单:{order.order_id}",
                            "reset": 0,
                            "security": "auto"
                        }

                        # 组织到面板和入站
                        if panel.id not in panel_clients:
                            panel_clients[panel.id] = {}
                        if inbound_id not in panel_clients[panel.id]:
                            panel_clients[panel.id][inbound_id] = []

                        panel_clients[panel.id][inbound_id].append({
                            'subscription_id': subscription.id,
                            'client_config': client_config
                        })

                except Exception as e:
                    logger.error(f"处理订阅 {subscription.id} 时发生错误: {e}")
                    continue

            # 执行批量同步
            results = {
                'total_subscriptions': len(subscriptions),
                'success_count': 0,
                'failed_count': 0,
                'panel_results': {}
            }

            for panel_id, inbound_clients in panel_clients.items():
                try:
                    panel = XUIPanel.query.get(panel_id)
                    if not panel:
                        continue

                    # 创建X-UI客户端
                    xui_client = XUIClient(
                        base_url=panel.base_url,
                        username=panel.username,
                        password=panel.password,
                        path_prefix=panel.path_prefix
                    )

                    panel_result = {
                        'panel_name': panel.name,
                        'inbound_results': {}
                    }

                    for inbound_id, client_data_list in inbound_clients.items():
                        try:
                            # 提取客户端配置
                            clients = [item['client_config'] for item in client_data_list]

                            # 批量添加客户端
                            success, response = xui_client.add_clients_batch(inbound_id, clients)

                            if success:
                                logger.info(f"成功批量添加 {len(clients)} 个客户端到面板 {panel.name} 入站 {inbound_id}")
                                results['success_count'] += len(clients)

                                panel_result['inbound_results'][inbound_id] = {
                                    'success': True,
                                    'client_count': len(clients),
                                    'message': '批量添加成功'
                                }
                            else:
                                error_msg = response.get('error', '未知错误')
                                logger.error(f"批量添加客户端到面板 {panel.name} 入站 {inbound_id} 失败: {error_msg}")
                                results['failed_count'] += len(clients)

                                panel_result['inbound_results'][inbound_id] = {
                                    'success': False,
                                    'client_count': len(clients),
                                    'message': error_msg
                                }

                        except Exception as e:
                            logger.error(f"处理面板 {panel.name} 入站 {inbound_id} 时发生错误: {e}")
                            results['failed_count'] += len(client_data_list)

                            panel_result['inbound_results'][inbound_id] = {
                                'success': False,
                                'client_count': len(client_data_list),
                                'message': str(e)
                            }

                    results['panel_results'][panel_id] = panel_result

                except Exception as e:
                    logger.error(f"处理面板 {panel_id} 时发生错误: {e}")
                    continue

            logger.info(f"批量同步完成: 总订阅 {results['total_subscriptions']}, 成功 {results['success_count']}, 失败 {results['failed_count']}")
            return results

        except Exception as e:
            logger.error(f"批量同步订阅时发生错误: {e}")
            return {
                'total_subscriptions': 0,
                'success_count': 0,
                'failed_count': 0,
                'error': str(e)
            }
