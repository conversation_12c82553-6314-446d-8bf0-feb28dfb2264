#!/usr/bin/env python3
"""
数据库迁移脚本：添加自定义协议类型支持
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app
from models import db, ProtocolTemplate, NodeType
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def migrate_add_custom_protocol_support():
    """添加自定义协议类型支持的数据库迁移"""
    app = create_app()
    
    with app.app_context():
        try:
            logger.info("开始数据库迁移：添加自定义协议类型支持")
            
            # 检查custom_protocol_name列是否已存在
            inspector = db.inspect(db.engine)
            columns = [col['name'] for col in inspector.get_columns('protocol_templates')]
            
            if 'custom_protocol_name' not in columns:
                logger.info("添加custom_protocol_name列到protocol_templates表...")
                with db.engine.connect() as conn:
                    conn.execute(db.text('''
                        ALTER TABLE protocol_templates 
                        ADD COLUMN custom_protocol_name VARCHAR(50)
                    '''))
                    conn.commit()
                logger.info("custom_protocol_name列添加成功")
            else:
                logger.info("custom_protocol_name列已存在，跳过添加")
            
            # 创建一个自定义协议模板示例
            logger.info("检查是否需要创建自定义协议模板示例...")
            existing_custom = ProtocolTemplate.query.filter_by(protocol_type=NodeType.CUSTOM).first()
            
            if not existing_custom:
                logger.info("创建自定义协议模板示例...")
                custom_template = ProtocolTemplate(
                    name='CUSTOM-HYSTERIA-UDP',
                    description='Hysteria UDP协议示例模板',
                    protocol_type=NodeType.CUSTOM,
                    custom_protocol_name='Hysteria',
                    template_content='hysteria://{client_id}@{server_address}:{server_port}?protocol=udp&auth={client_email}&upmbps=100&downmbps=100#{client_email}',
                    is_active=True,
                    is_default=False
                )
                
                db.session.add(custom_template)
                db.session.commit()
                logger.info("自定义协议模板示例创建成功")
            else:
                logger.info("自定义协议模板已存在，跳过创建")
            
            logger.info("数据库迁移完成！")
            
        except Exception as e:
            logger.error(f"数据库迁移失败: {e}")
            db.session.rollback()
            raise

def rollback_custom_protocol_support():
    """回滚自定义协议类型支持（仅用于开发测试）"""
    app = create_app()
    
    with app.app_context():
        try:
            logger.info("开始回滚自定义协议类型支持...")
            
            # 删除自定义协议模板
            logger.info("删除自定义协议模板...")
            custom_templates = ProtocolTemplate.query.filter_by(protocol_type=NodeType.CUSTOM).all()
            for template in custom_templates:
                logger.info(f"删除自定义模板: {template.name}")
                db.session.delete(template)
            
            db.session.commit()
            
            # 删除custom_protocol_name列
            logger.info("移除custom_protocol_name列...")
            try:
                with db.engine.connect() as conn:
                    conn.execute(db.text('''
                        ALTER TABLE protocol_templates 
                        DROP COLUMN custom_protocol_name
                    '''))
                    conn.commit()
                logger.info("custom_protocol_name列移除成功")
            except Exception as e:
                logger.warning(f"移除custom_protocol_name列失败: {e}")
            
            logger.info("自定义协议类型支持回滚完成")
            
        except Exception as e:
            logger.error(f"回滚失败: {e}")
            db.session.rollback()
            raise

def check_migration_status():
    """检查迁移状态"""
    app = create_app()
    
    with app.app_context():
        try:
            # 检查custom_protocol_name列是否存在
            inspector = db.inspect(db.engine)
            columns = [col['name'] for col in inspector.get_columns('protocol_templates')]
            
            if 'custom_protocol_name' in columns:
                logger.info("✓ custom_protocol_name列已存在")
            else:
                logger.info("✗ custom_protocol_name列不存在")
            
            # 检查自定义协议模板
            custom_count = ProtocolTemplate.query.filter_by(protocol_type=NodeType.CUSTOM).count()
            logger.info(f"✓ 自定义协议模板数量: {custom_count}")
            
            # 列出所有协议模板
            all_templates = ProtocolTemplate.query.all()
            logger.info(f"✓ 协议模板总数: {len(all_templates)}")
            
            for template in all_templates:
                protocol_display = template.protocol_type_display
                logger.info(f"  - {template.name}: {protocol_display} ({'活跃' if template.is_active else '禁用'})")
                
        except Exception as e:
            logger.error(f"检查迁移状态失败: {e}")

if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='自定义协议类型支持数据库迁移')
    parser.add_argument('--rollback', action='store_true', help='回滚自定义协议类型支持（仅用于开发测试）')
    parser.add_argument('--status', action='store_true', help='检查迁移状态')
    
    args = parser.parse_args()
    
    if args.status:
        check_migration_status()
    elif args.rollback:
        confirm = input("确定要回滚自定义协议类型支持吗？这将删除所有自定义协议模板！(yes/no): ")
        if confirm.lower() == 'yes':
            rollback_custom_protocol_support()
        else:
            print("回滚操作已取消")
    else:
        migrate_add_custom_protocol_support()
