<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>最终加载测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container py-4">
        <h1>最终加载状态测试</h1>
        
        <div class="alert alert-info">
            <h5>测试说明</h5>
            <p>这个页面使用与主页面相同的JavaScript代码，用于验证加载状态是否正确工作。</p>
        </div>
        
        <div class="row">
            <div class="col-12">
                <button class="btn btn-primary me-2" onclick="testNormalAPI()">正常API测试</button>
                <button class="btn btn-warning me-2" onclick="testErrorAPI()">错误API测试</button>
                <button class="btn btn-info me-2" onclick="testTimeout()">超时测试</button>
                <button class="btn btn-success" onclick="clearLog()">清除日志</button>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">测试日志</div>
                    <div class="card-body">
                        <div id="test-log" style="max-height: 400px; overflow-y: auto; font-family: monospace; font-size: 12px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function addTestLog(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'red' : type === 'success' ? 'green' : type === 'warning' ? 'orange' : 'blue';
            logDiv.innerHTML += `<div style="color: ${color};">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
        }
        
        // 使用与主页面相同的showLoading和hideLoading函数
        function showLoading(message) {
            addTestLog(`🔄 showLoading: ${message}`);
            
            if (window.loadingTimeout) {
                clearTimeout(window.loadingTimeout);
            }
            
            let loadingDiv = document.getElementById('loading-indicator');
            if (!loadingDiv) {
                loadingDiv = document.createElement('div');
                loadingDiv.id = 'loading-indicator';
                loadingDiv.className = 'alert alert-info d-flex align-items-center mt-3';
                loadingDiv.innerHTML = `
                    <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                    <span id="loading-message">${message}</span>
                `;
                document.querySelector('.container').appendChild(loadingDiv);
                addTestLog('✅ 加载指示器已创建');
            } else {
                document.getElementById('loading-message').textContent = message;
                loadingDiv.style.display = 'flex';
                addTestLog('✅ 加载指示器已更新');
            }
            
            window.loadingTimeout = setTimeout(() => {
                addTestLog('⚠️ 30秒超时，自动隐藏加载状态', 'warning');
                hideLoading();
            }, 30000);
        }
        
        function hideLoading() {
            addTestLog('🔄 hideLoading: 开始隐藏');
            
            if (window.loadingTimeout) {
                clearTimeout(window.loadingTimeout);
                window.loadingTimeout = null;
                addTestLog('✅ 已清除超时定时器');
            }
            
            const loadingDiv = document.getElementById('loading-indicator');
            if (loadingDiv) {
                loadingDiv.style.display = 'none';
                addTestLog('✅ 加载状态已隐藏', 'success');
            } else {
                addTestLog('❌ 找不到加载指示器', 'error');
            }
        }
        
        function testNormalAPI() {
            addTestLog('🧪 开始正常API测试');
            showLoading('正在测试正常API...');
            
            fetch('/admin/api/traffic-optimization/stats')
                .then(response => {
                    addTestLog(`📡 收到响应: ${response.status}`);
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    addTestLog(`✅ API调用成功: ${data.success}`, 'success');
                    hideLoading();
                })
                .catch(error => {
                    addTestLog(`❌ API调用失败: ${error.message}`, 'error');
                    hideLoading();
                });
        }
        
        function testErrorAPI() {
            addTestLog('🧪 开始错误API测试');
            showLoading('正在测试错误API...');
            
            fetch('/admin/api/nonexistent-endpoint')
                .then(response => {
                    addTestLog(`📡 收到响应: ${response.status}`);
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    addTestLog(`✅ 意外成功: ${data}`, 'success');
                    hideLoading();
                })
                .catch(error => {
                    addTestLog(`✅ 正确捕获错误: ${error.message}`, 'success');
                    hideLoading();
                });
        }
        
        function testTimeout() {
            addTestLog('🧪 开始超时测试（30秒）');
            showLoading('测试30秒超时保护...');
            addTestLog('⏰ 等待30秒自动超时...');
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            addTestLog('🚀 最终测试页面加载完成', 'success');
        });
    </script>
</body>
</html>