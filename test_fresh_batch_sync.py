#!/usr/bin/env python3
"""
测试全新的批量同步功能 - 清理后重新同步
"""
from app import create_app
from models import db, Subscription, Order, XUIPanelGroup, XUIPanel, NodeConfig
from services.subscription_sync_service import SubscriptionSyncService
import time
from datetime import datetime

def cleanup_existing_nodes():
    """清理现有节点配置以便测试全新同步"""
    print('清理现有节点配置...')
    
    # 获取所有节点配置
    all_nodes = NodeConfig.query.all()
    print(f'  找到 {len(all_nodes)} 个现有节点配置')
    
    # 删除所有节点配置
    for node in all_nodes:
        db.session.delete(node)
    
    db.session.commit()
    print('  ✓ 所有节点配置已清理')

def test_fresh_batch_sync():
    """测试全新的批量同步"""
    app = create_app()
    with app.app_context():
        print('=== 全新批量同步功能测试 ===\n')
        
        # 1. 系统状态
        groups = XUIPanelGroup.query.all()
        if not groups:
            print('❌ 没有面板分组')
            return
        
        test_group = groups[0]
        panels = test_group.panels
        subscriptions = Subscription.query.filter_by(is_active=True).all()
        
        print(f'1. 测试环境:')
        print(f'   分组: {test_group.name}')
        print(f'   面板数: {len(panels)}')
        print(f'   订阅数: {len(subscriptions)}')
        print(f'   预期节点数: {len(subscriptions)} × {len(panels)} = {len(subscriptions) * len(panels)}')
        
        # 2. 清理现有节点
        print(f'\n2. 清理现有节点:')
        cleanup_existing_nodes()
        
        # 3. 验证清理效果
        remaining_nodes = NodeConfig.query.count()
        print(f'   清理后剩余节点: {remaining_nodes}')
        
        # 4. 执行全新批量同步
        print(f'\n3. 执行全新批量同步:')
        sync_service = SubscriptionSyncService()
        
        start_time = time.time()
        print(f'   开始时间: {datetime.now().strftime("%H:%M:%S")}')
        
        result = sync_service.sync_group_all_panels(test_group.id)
        
        end_time = time.time()
        duration = end_time - start_time
        print(f'   结束时间: {datetime.now().strftime("%H:%M:%S")}')
        print(f'   耗时: {duration:.2f} 秒')
        
        # 5. 分析结果
        print(f'\n4. 同步结果:')
        print(f'   成功: {result["success"]}')
        print(f'   影响订阅: {result.get("affected_subscriptions", 0)}')
        print(f'   成功数量: {result.get("success_count", 0)}')
        print(f'   失败数量: {result.get("failed_count", 0)}')
        
        # 面板详情
        panel_results = result.get('panel_results', [])
        if panel_results:
            print(f'\n   面板详情:')
            for panel_result in panel_results:
                name = panel_result.get('panel_name', '未知')
                success = panel_result.get('success_count', 0)
                failed = panel_result.get('failed_count', 0)
                status = '✓' if panel_result.get('success', False) else '❌'
                
                print(f'     {status} {name}: 成功 {success}, 失败 {failed}')
                
                if panel_result.get('message'):
                    print(f'         消息: {panel_result["message"]}')
                if panel_result.get('error'):
                    print(f'         错误: {panel_result["error"]}')
        
        # 6. 验证数据库
        print(f'\n5. 验证数据库:')
        new_nodes = NodeConfig.query.filter_by(is_active=True).count()
        print(f'   新创建的活跃节点: {new_nodes}')
        
        # 按面板统计
        for panel in panels:
            server_address = panel.base_url.replace('http://', '').replace('https://', '').split(':')[0]
            panel_nodes = NodeConfig.query.filter(
                NodeConfig.server_address == server_address,
                NodeConfig.is_active == True
            ).count()
            print(f'     面板 {panel.name}: {panel_nodes} 个节点')
        
        # 7. 检查协议配置
        print(f'\n6. 检查协议配置:')
        sample_nodes = NodeConfig.query.filter_by(is_active=True).limit(3).all()
        for i, node in enumerate(sample_nodes, 1):
            config = node.vless_config
            protocol = 'Unknown'
            if config.startswith('hysteria://'):
                protocol = 'Hysteria'
            elif config.startswith('vless://'):
                protocol = 'VLESS'
            elif config.startswith('vmess://'):
                protocol = 'VMess'
            elif config.startswith('trojan://'):
                protocol = 'Trojan'
            
            print(f'   节点 {i}: {protocol} 协议')
            print(f'     服务器: {node.server_address}:{node.server_port}')
            print(f'     配置: {config[:50]}...')
        
        # 8. 性能分析
        print(f'\n7. 性能分析:')
        total_ops = len(subscriptions) * len(panels)
        if duration > 0 and total_ops > 0:
            ops_per_sec = total_ops / duration
            print(f'   总操作数: {total_ops}')
            print(f'   操作速度: {ops_per_sec:.2f} 操作/秒')
            print(f'   每订阅耗时: {duration/len(subscriptions):.3f} 秒')
            
            # 与单个同步对比
            estimated_single = total_ops * 0.3  # 假设单个操作0.3秒
            improvement = (estimated_single - duration) / estimated_single * 100
            print(f'   预估单个同步耗时: {estimated_single:.2f} 秒')
            print(f'   性能提升: {improvement:.1f}%')
        
        print(f'\n=== 测试完成 ===')

if __name__ == '__main__':
    test_fresh_batch_sync()
