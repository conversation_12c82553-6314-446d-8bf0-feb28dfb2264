#!/usr/bin/env python3
"""
测试订单创建时使用协议模板功能
"""
from app import create_app
from models import db, ProtocolTemplate, XUIPanel, XUIPanelGroup, XUIPanelGroupMembership, Order, NodeConfig
from utils.order_service import OrderService
import uuid

def main():
    app = create_app()
    with app.app_context():
        print('=== 测试订单创建使用协议模板功能 ===\n')
        
        # 1. 检查当前协议模板和面板分组情况
        print('1. 当前系统状态:')
        
        # 协议模板
        templates = ProtocolTemplate.query.filter_by(is_active=True).all()
        print(f'   活跃协议模板: {len(templates)} 个')
        for template in templates:
            print(f'     - {template.name} ({template.protocol_type_display})')
        
        # 面板分组
        groups = XUIPanelGroup.query.all()
        print(f'\n   面板分组: {len(groups)} 个')
        for group in groups:
            print(f'     分组: {group.name}')
            for membership in group.memberships:
                panel = membership.panel
                template_info = '默认VLESS'
                if membership.protocol_template_id:
                    template = ProtocolTemplate.query.get(membership.protocol_template_id)
                    if template:
                        template_info = f'{template.name} ({template.protocol_type_display})'
                
                print(f'       - 面板: {panel.name} -> 协议: {template_info}')
        
        # 2. 为测试分组的面板分配协议模板
        print(f'\n2. 配置测试环境:')
        
        # 找到第一个分组和面板
        if groups:
            test_group = groups[0]
            if test_group.memberships:
                test_membership = test_group.memberships[0]
                
                # 为面板分配一个自定义协议模板
                custom_template = ProtocolTemplate.query.filter_by(protocol_type='custom').first()
                if custom_template:
                    print(f'   为面板 {test_membership.panel.name} 分配协议模板: {custom_template.name}')
                    test_membership.protocol_template_id = custom_template.id
                    db.session.commit()
                    print(f'   ✓ 协议模板分配成功')
                else:
                    print(f'   ⚠ 未找到自定义协议模板，将使用默认VLESS')
        
        # 3. 创建测试订单
        print(f'\n3. 创建测试订单:')

        # 首先检查可用的产品
        from models import Product
        products = Product.query.all()
        print(f'   可用产品数量: {len(products)}')

        group_product = None
        for product in products:
            print(f'     - {product.name} (类型: {product.product_type.value})')
            if hasattr(product, 'target_group_id') and product.target_group_id:
                group_product = product
                print(f'       ✓ 这是分组产品，分组ID: {product.target_group_id}')

        order_service = OrderService()
        test_email = f'test_{uuid.uuid4().hex[:8]}@example.com'

        if group_product:
            print(f'   使用分组产品: {group_product.name}')
            # 创建订单（使用分组产品的配置）
            success, order = order_service.create_order(
                customer_email=test_email,
                customer_name='测试用户',
                node_type=group_product.node_type.value,
                duration_days=group_product.duration_days,
                traffic_limit_gb=group_product.traffic_limit_gb,
                price=0,
                test_mode=True
            )

            # 手动关联产品
            if success and order:
                order.product_id = group_product.id
                db.session.commit()
                print(f'   ✓ 订单已关联到产品: {group_product.name}')
        else:
            print(f'   未找到分组产品，使用默认创建方式')
            # 创建订单（使用默认方式）
            success, order = order_service.create_order(
                customer_email=test_email,
                customer_name='测试用户',
                node_type='vless',  # 这里的node_type会被协议模板覆盖
                duration_days=30,
                traffic_limit_gb=100,
                price=0,
                test_mode=True
            )
        
        if success and order:
            print(f'   ✓ 订单创建成功: {order.order_id}')
            print(f'   订单邮箱: {order.customer_email}')
            print(f'   订单状态: {order.status.value}')

            # 处理订单（创建节点配置）
            print(f'\n   处理订单...')
            process_success, process_message = order_service.process_order(order.order_id)
            if process_success:
                print(f'   ✓ 订单处理成功: {process_message}')
            else:
                print(f'   ✗ 订单处理失败: {process_message}')

            # 4. 检查节点配置
            print(f'\n4. 检查节点配置:')
            node_configs = NodeConfig.query.filter_by(order_id=order.id).all()
            print(f'   节点配置数量: {len(node_configs)}')
            
            for i, config in enumerate(node_configs, 1):
                print(f'\n   节点 {i}:')
                print(f'     服务器地址: {config.server_address}')
                print(f'     服务器端口: {config.server_port}')
                print(f'     协议类型: {config.protocol}')
                print(f'     客户端ID: {config.client_id}')
                print(f'     客户端邮箱: {config.client_email}')
                
                if config.vless_config:
                    # 检查配置是否使用了协议模板
                    config_str = config.vless_config
                    print(f'     配置字符串: {config_str[:100]}...')
                    
                    # 分析配置类型
                    if config_str.startswith('hysteria://'):
                        print(f'     ✓ 使用了自定义协议模板 (Hysteria)')
                    elif config_str.startswith('vless://'):
                        print(f'     ℹ 使用了VLESS协议')
                    elif config_str.startswith('vmess://'):
                        print(f'     ℹ 使用了VMess协议')
                    elif config_str.startswith('trojan://'):
                        print(f'     ℹ 使用了Trojan协议')
                    else:
                        print(f'     ? 未知协议类型')
                else:
                    print(f'     ✗ 配置字符串为空')
            
            # 5. 测试订阅生成
            print(f'\n5. 测试订阅生成:')
            from models import Subscription

            # 查找订单关联的订阅
            subscription = Subscription.query.filter_by(order_id=order.id).first()
            if subscription:
                print(f'   ✓ 订阅已创建: {subscription.subscription_token[:16]}...')

                # 生成订阅配置
                from services.subscription_service import SubscriptionService
                subscription_service = SubscriptionService()
                config = subscription_service.get_order_subscription_configs(subscription.subscription_token)
                if config:
                    print(f'   ✓ 订阅配置生成成功')

                    # 解码查看内容
                    import base64
                    try:
                        decoded_config = base64.b64decode(config).decode('utf-8')
                        lines = decoded_config.split('\n')
                        print(f'   订阅包含 {len(lines)} 个节点配置')

                        for i, line in enumerate(lines, 1):
                            if line.strip():
                                protocol = line.split('://')[0] if '://' in line else 'unknown'
                                print(f'     节点 {i}: {protocol.upper()} 协议')
                    except Exception as e:
                        print(f'   ⚠ 解码订阅配置失败: {e}')
                else:
                    print(f'   ✗ 订阅配置生成失败')
            else:
                print(f'   ✗ 未找到订阅记录')
        else:
            print(f'   ✗ 订单创建失败')
        
        print(f'\n=== 测试完成 ===')

if __name__ == '__main__':
    main()
