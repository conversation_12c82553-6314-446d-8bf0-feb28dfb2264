#!/usr/bin/env python3
"""
检查应用程序状态
"""
from app import create_app
from models import db, XUIPanel, Subscription, Order, User
import os

def check_app_status():
    """检查应用程序状态"""
    app = create_app()
    with app.app_context():
        print('=== 应用程序状态检查 ===\n')
        
        # 1. 数据库连接检查
        print('1. 数据库连接检查:')
        
        try:
            db_path = app.config.get('SQLALCHEMY_DATABASE_URI', '').replace('sqlite:///', '')
            print(f'   数据库文件: {db_path}')
            
            if os.path.exists(db_path):
                db_size = os.path.getsize(db_path) / (1024 * 1024)  # MB
                print(f'   文件大小: {db_size:.2f} MB')
                print(f'   ✅ 数据库文件存在')
            else:
                print(f'   ❌ 数据库文件不存在')
                return
                
        except Exception as e:
            print(f'   ❌ 数据库连接失败: {e}')
            return
        
        # 2. 核心模型查询测试
        print(f'\n2. 核心模型查询测试:')
        
        try:
            # 测试面板查询
            panels = XUIPanel.query.all()
            print(f'   ✅ 面板查询成功: {len(panels)} 个面板')
            
            for panel in panels:
                print(f'     - {panel.name}: {panel.status.value}')
                
        except Exception as e:
            print(f'   ❌ 面板查询失败: {e}')
        
        try:
            # 测试订阅查询
            subscriptions = Subscription.query.all()
            active_subs = Subscription.query.filter_by(is_active=True).all()
            print(f'   ✅ 订阅查询成功: {len(subscriptions)} 个订阅 (活跃: {len(active_subs)})')
            
        except Exception as e:
            print(f'   ❌ 订阅查询失败: {e}')
        
        try:
            # 测试订单查询
            orders = Order.query.all()
            print(f'   ✅ 订单查询成功: {len(orders)} 个订单')
            
        except Exception as e:
            print(f'   ❌ 订单查询失败: {e}')
        
        try:
            # 测试用户查询
            users = User.query.all()
            admins = User.query.filter_by(role='ADMIN').all()
            print(f'   ✅ 用户查询成功: {len(users)} 个用户 (管理员: {len(admins)})')
            
        except Exception as e:
            print(f'   ❌ 用户查询失败: {e}')
        
        # 3. 枚举值检查
        print(f'\n3. 枚举值检查:')
        
        try:
            from models import PanelStatus, UserRole, ProductType, NodeType
            
            print(f'   ✅ 枚举导入成功')
            print(f'     PanelStatus: {[status.value for status in PanelStatus]}')
            print(f'     UserRole: {[role.value for role in UserRole]}')
            print(f'     ProductType: {[ptype.value for ptype in ProductType]}')
            print(f'     NodeType: {[ntype.value for ntype in NodeType]}')
            
        except Exception as e:
            print(f'   ❌ 枚举导入失败: {e}')
        
        # 4. 服务状态检查
        print(f'\n4. 服务状态检查:')
        
        try:
            from services.config_service import config_service
            print(f'   ✅ 配置服务: 正常')
            
        except Exception as e:
            print(f'   ❌ 配置服务: {e}')
        
        try:
            from services.scheduler_service import scheduler_service
            print(f'   ✅ 定时任务服务: 正常')
            
        except Exception as e:
            print(f'   ❌ 定时任务服务: {e}')
        
        try:
            from services.subscription_service import SubscriptionService
            print(f'   ✅ 订阅服务: 正常')
            
        except Exception as e:
            print(f'   ❌ 订阅服务: {e}')
        
        # 5. 路由检查
        print(f'\n5. 路由检查:')
        
        try:
            from routes.admin import admin_bp
            from routes.user import user_bp
            from routes.auth import auth_bp
            
            print(f'   ✅ 管理员路由: 正常')
            print(f'   ✅ 用户路由: 正常')
            print(f'   ✅ 认证路由: 正常')
            
        except Exception as e:
            print(f'   ❌ 路由导入失败: {e}')
        
        # 6. 配置检查
        print(f'\n6. 配置检查:')
        
        try:
            print(f'   数据库URI: {app.config.get("SQLALCHEMY_DATABASE_URI", "未设置")}')
            print(f'   调试模式: {app.config.get("DEBUG", False)}')
            print(f'   密钥设置: {"已设置" if app.config.get("SECRET_KEY") else "未设置"}')
            
        except Exception as e:
            print(f'   ❌ 配置检查失败: {e}')
        
        # 7. 应用程序健康度评估
        print(f'\n7. 应用程序健康度评估:')
        
        health_score = 0
        max_score = 6
        
        # 数据库连接 (1分)
        try:
            db.session.execute('SELECT 1')
            health_score += 1
            print(f'   ✅ 数据库连接: 正常 (+1)')
        except:
            print(f'   ❌ 数据库连接: 异常 (+0)')
        
        # 模型查询 (1分)
        try:
            XUIPanel.query.first()
            health_score += 1
            print(f'   ✅ 模型查询: 正常 (+1)')
        except:
            print(f'   ❌ 模型查询: 异常 (+0)')
        
        # 枚举值 (1分)
        try:
            from models import PanelStatus
            health_score += 1
            print(f'   ✅ 枚举值: 正常 (+1)')
        except:
            print(f'   ❌ 枚举值: 异常 (+0)')
        
        # 服务导入 (1分)
        try:
            from services.config_service import config_service
            health_score += 1
            print(f'   ✅ 服务导入: 正常 (+1)')
        except:
            print(f'   ❌ 服务导入: 异常 (+0)')
        
        # 路由导入 (1分)
        try:
            from routes.admin import admin_bp
            health_score += 1
            print(f'   ✅ 路由导入: 正常 (+1)')
        except:
            print(f'   ❌ 路由导入: 异常 (+0)')
        
        # 配置完整性 (1分)
        if app.config.get('SECRET_KEY') and app.config.get('SQLALCHEMY_DATABASE_URI'):
            health_score += 1
            print(f'   ✅ 配置完整性: 正常 (+1)')
        else:
            print(f'   ❌ 配置完整性: 异常 (+0)')
        
        # 计算健康度
        health_percentage = (health_score / max_score) * 100
        
        print(f'\n   📊 应用程序健康度: {health_score}/{max_score} ({health_percentage:.1f}%)')
        
        if health_percentage >= 90:
            print(f'   🟢 状态: 优秀')
        elif health_percentage >= 70:
            print(f'   🟡 状态: 良好')
        elif health_percentage >= 50:
            print(f'   🟠 状态: 一般')
        else:
            print(f'   🔴 状态: 需要修复')
        
        # 8. 建议
        print(f'\n8. 建议:')
        
        if health_percentage >= 90:
            print(f'   ✅ 应用程序状态良好，可以正常使用')
            print(f'   💡 建议: 定期检查应用程序状态')
        elif health_percentage >= 70:
            print(f'   ⚠️ 应用程序基本正常，但有小问题')
            print(f'   💡 建议: 检查并修复发现的问题')
        else:
            print(f'   ❌ 应用程序存在严重问题')
            print(f'   💡 建议: 立即修复问题或重启应用程序')
        
        print(f'\n=== 应用程序状态检查完成 ===')

if __name__ == '__main__':
    check_app_status()
