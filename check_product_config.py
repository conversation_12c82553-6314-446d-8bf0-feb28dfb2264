#!/usr/bin/env python3
"""
检查产品和分组配置
"""
from app import create_app
from models import db, Product, XUIPanelGroup

def main():
    app = create_app()
    with app.app_context():
        print('=== 检查产品和分组配置 ===\n')
        
        # 检查产品
        products = Product.query.all()
        print(f'产品数量: {len(products)}')
        for product in products:
            print(f'  产品: {product.name}')
            print(f'    ID: {product.id}')
            print(f'    类型: {product.product_type.value}')
            group_id = getattr(product, 'group_id', None)
            print(f'    分组ID: {group_id if group_id else "无"}')
            print()
        
        # 检查分组
        groups = XUIPanelGroup.query.all()
        print(f'分组数量: {len(groups)}')
        for group in groups:
            print(f'  分组: {group.name}')
            print(f'    ID: {group.id}')
            print(f'    描述: {group.description}')
            print()

if __name__ == '__main__':
    main()
