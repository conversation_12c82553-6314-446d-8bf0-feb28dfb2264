<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流量优化调试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container py-4">
        <h1>流量优化功能调试</h1>
        
        <div class="row mt-4">
            <div class="col-12">
                <button type="button" class="btn btn-warning" onclick="testPreview()">测试预览</button>
                <button type="button" class="btn btn-danger" onclick="testCleanup()">测试清理</button>
                <button type="button" class="btn btn-info" onclick="testStats()">测试统计</button>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">调试日志</div>
                    <div class="card-body">
                        <div id="debug-log" style="max-height: 400px; overflow-y: auto; font-family: monospace; font-size: 12px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function addDebugLog(message) {
            const logDiv = document.getElementById('debug-log');
            const timestamp = new Date().toISOString();
            logDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function showLoading(message) {
            addDebugLog(`🔄 showLoading: ${message}`);
            let loadingDiv = document.getElementById('loading-indicator');
            if (!loadingDiv) {
                loadingDiv = document.createElement('div');
                loadingDiv.id = 'loading-indicator';
                loadingDiv.className = 'alert alert-info d-flex align-items-center mt-3';
                loadingDiv.innerHTML = `
                    <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                    <span id="loading-message">${message}</span>
                `;
                document.querySelector('.container').appendChild(loadingDiv);
            } else {
                document.getElementById('loading-message').textContent = message;
                loadingDiv.style.display = 'flex';
            }
        }
        
        function hideLoading() {
            addDebugLog('✅ hideLoading: 隐藏加载状态');
            const loadingDiv = document.getElementById('loading-indicator');
            if (loadingDiv) {
                loadingDiv.style.display = 'none';
            }
        }
        
        function testStats() {
            addDebugLog('📊 开始测试统计API...');
            showLoading('正在获取统计信息...');
            
            fetch('/admin/api/traffic-optimization/stats')
                .then(response => {
                    addDebugLog(`📊 统计API响应: ${response.status}`);
                    return response.json();
                })
                .then(data => {
                    addDebugLog(`📊 统计API数据: ${JSON.stringify(data).substring(0, 100)}...`);
                    hideLoading();
                })
                .catch(error => {
                    addDebugLog(`❌ 统计API错误: ${error.message}`);
                    hideLoading();
                });
        }
        
        function testPreview() {
            addDebugLog('👁️ 开始测试预览API...');
            showLoading('正在生成清理预览...');
            
            fetch('/admin/api/traffic-optimization/preview')
                .then(response => {
                    addDebugLog(`👁️ 预览API响应: ${response.status}`);
                    return response.json();
                })
                .then(data => {
                    addDebugLog(`👁️ 预览API数据: ${JSON.stringify(data).substring(0, 100)}...`);
                    hideLoading();
                })
                .catch(error => {
                    addDebugLog(`❌ 预览API错误: ${error.message}`);
                    hideLoading();
                });
        }
        
        function testCleanup() {
            addDebugLog('🧹 开始测试清理检查...');
            showLoading('正在检查清理数据...');
            
            fetch('/admin/api/traffic-optimization/preview')
                .then(response => {
                    addDebugLog(`🧹 清理检查响应: ${response.status}`);
                    return response.json();
                })
                .then(data => {
                    addDebugLog(`🧹 清理检查数据: ${JSON.stringify(data).substring(0, 100)}...`);
                    hideLoading();
                    
                    if (data.success && data.preview && data.preview.will_delete_count > 0) {
                        addDebugLog(`🧹 发现 ${data.preview.will_delete_count} 条可清理记录`);
                    } else {
                        addDebugLog('🧹 没有需要清理的数据');
                    }
                })
                .catch(error => {
                    addDebugLog(`❌ 清理检查错误: ${error.message}`);
                    hideLoading();
                });
        }
        
        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            addDebugLog('🚀 页面加载完成，调试工具就绪');
        });
    </script>
</body>
</html>