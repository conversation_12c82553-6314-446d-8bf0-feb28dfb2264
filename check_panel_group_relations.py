#!/usr/bin/env python3
"""
检查面板和分组关系
"""
from app import create_app
from models import db, XUIPanel, XUIPanelGroup, XUIPanelGroupMembership

def main():
    app = create_app()
    with app.app_context():
        print('=== 检查面板和分组关系 ===\n')
        
        # 检查面板
        panels = XUIPanel.query.all()
        print(f'数据库中的面板数量: {len(panels)}')
        for panel in panels:
            print(f'  面板: {panel.name} (ID: {panel.id})')
        
        # 检查分组
        groups = XUIPanelGroup.query.all()
        print(f'\n数据库中的分组数量: {len(groups)}')
        for group in groups:
            print(f'  分组: {group.name} (ID: {group.id})')
        
        # 检查成员关系
        memberships = XUIPanelGroupMembership.query.all()
        print(f'\n成员关系数量: {len(memberships)}')
        for membership in memberships:
            panel = XUIPanel.query.get(membership.panel_id)
            group = XUIPanelGroup.query.get(membership.group_id)
            panel_name = panel.name if panel else "未知"
            group_name = group.name if group else "未知"
            print(f'  关系: 面板 {panel_name} -> 分组 {group_name}')
            print(f'    面板ID: {membership.panel_id}, 分组ID: {membership.group_id}')
            print(f'    入站ID: {membership.inbound_id}')
            print(f'    协议模板ID: {membership.protocol_template_id}')
            print()
        
        # 如果没有成员关系，尝试创建一个
        if not memberships and panels and groups:
            print('没有成员关系，尝试创建一个测试关系...')
            panel = panels[0]
            group = groups[0]
            
            membership = XUIPanelGroupMembership(
                panel_id=panel.id,
                group_id=group.id,
                inbound_id=1,
                protocol_template_id=7  # 使用Hysteria协议模板
            )
            
            db.session.add(membership)
            db.session.commit()
            
            print(f'✓ 创建成员关系: 面板 {panel.name} -> 分组 {group.name}')
            print(f'  入站ID: 1, 协议模板ID: 7')

if __name__ == '__main__':
    main()
