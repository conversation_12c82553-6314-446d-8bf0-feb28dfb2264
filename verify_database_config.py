#!/usr/bin/env python3
"""
验证数据库配置修改效果
"""
from app import create_app
from models import db, XUIPanel, Subscription
import os

def verify_database_config():
    """验证数据库配置修改效果"""
    print('=== 验证数据库配置修改效果 ===\n')
    
    # 1. 检查修改后的配置
    print('1. 检查修改后的配置:')
    
    app = create_app()
    with app.app_context():
        db_uri = app.config['SQLALCHEMY_DATABASE_URI']
        print(f'   修改后的数据库URI: {db_uri}')
        
        # 解析数据库路径
        if db_uri.startswith('sqlite:///'):
            db_path = db_uri.replace('sqlite:///', '')
            print(f'   数据库文件路径: {db_path}')
            
            # 检查是否是绝对路径
            if os.path.isabs(db_path):
                print(f'   ✅ 现在使用绝对路径')
            else:
                print(f'   ❌ 仍然是相对路径')
        
        # 2. 检查实际连接的数据库
        print(f'\n2. 检查实际连接的数据库:')
        
        try:
            engine = db.engine
            actual_url = str(engine.url)
            print(f'   实际数据库URL: {actual_url}')
            
            # 提取实际文件路径
            if actual_url.startswith('sqlite:///'):
                actual_path = actual_url.replace('sqlite:///', '')
                print(f'   实际数据库文件: {actual_path}')
                
                # 检查文件是否存在
                if os.path.exists(actual_path):
                    file_size = os.path.getsize(actual_path) / (1024 * 1024)  # MB
                    print(f'   文件大小: {file_size:.2f} MB')
                    print(f'   ✅ 数据库文件存在')
                else:
                    print(f'   ❌ 数据库文件不存在')
                    
        except Exception as e:
            print(f'   检查数据库连接失败: {e}')
        
        # 3. 测试数据库查询
        print(f'\n3. 测试数据库查询:')
        
        try:
            # 查询面板
            panels = XUIPanel.query.all()
            print(f'   ✅ 面板查询成功: {len(panels)} 个面板')
            
            for panel in panels:
                print(f'     - {panel.name}: {panel.status.value}')
            
            # 查询订阅
            subscriptions = Subscription.query.all()
            active_subs = Subscription.query.filter_by(is_active=True).all()
            print(f'   ✅ 订阅查询成功: {len(subscriptions)} 个订阅 (活跃: {len(active_subs)})')
            
        except Exception as e:
            print(f'   ❌ 数据库查询失败: {e}')
        
        # 4. 比较两个数据库文件
        print(f'\n4. 比较两个数据库文件:')
        
        root_db = 'node_sales.db'
        instance_db = os.path.join('instance', 'node_sales.db')
        
        print(f'   根目录数据库: {root_db}')
        if os.path.exists(root_db):
            root_size = os.path.getsize(root_db) / (1024 * 1024)
            print(f'     文件大小: {root_size:.2f} MB')
            print(f'     ✅ 文件存在')
        else:
            print(f'     ❌ 文件不存在')
        
        print(f'   实例目录数据库: {instance_db}')
        if os.path.exists(instance_db):
            instance_size = os.path.getsize(instance_db) / (1024 * 1024)
            print(f'     文件大小: {instance_size:.2f} MB')
            print(f'     ✅ 文件存在')
        else:
            print(f'     ❌ 文件不存在')
        
        # 5. 检查数据内容差异
        print(f'\n5. 检查数据内容差异:')
        
        if os.path.exists(root_db) and os.path.exists(instance_db):
            import sqlite3
            
            # 检查根目录数据库
            try:
                conn_root = sqlite3.connect(root_db)
                cursor_root = conn_root.cursor()
                
                cursor_root.execute("SELECT COUNT(*) FROM xui_panels")
                root_panels = cursor_root.fetchone()[0]
                
                cursor_root.execute("SELECT COUNT(*) FROM subscriptions")
                root_subs = cursor_root.fetchone()[0]
                
                conn_root.close()
                
                print(f'   根目录数据库内容:')
                print(f'     面板数量: {root_panels}')
                print(f'     订阅数量: {root_subs}')
                
            except Exception as e:
                print(f'   检查根目录数据库失败: {e}')
            
            # 检查实例目录数据库
            try:
                conn_instance = sqlite3.connect(instance_db)
                cursor_instance = conn_instance.cursor()
                
                cursor_instance.execute("SELECT COUNT(*) FROM xui_panels")
                instance_panels = cursor_instance.fetchone()[0]
                
                cursor_instance.execute("SELECT COUNT(*) FROM subscriptions")
                instance_subs = cursor_instance.fetchone()[0]
                
                conn_instance.close()
                
                print(f'   实例目录数据库内容:')
                print(f'     面板数量: {instance_panels}')
                print(f'     订阅数量: {instance_subs}')
                
                # 比较差异
                if root_panels != instance_panels or root_subs != instance_subs:
                    print(f'   ⚠️ 两个数据库内容不同！')
                    print(f'   建议: 选择数据更完整的数据库作为主数据库')
                else:
                    print(f'   ✅ 两个数据库内容相同')
                
            except Exception as e:
                print(f'   检查实例目录数据库失败: {e}')
        
        # 6. 建议下一步操作
        print(f'\n6. 建议下一步操作:')
        
        print(f'   配置已修改为使用根目录数据库文件')
        print(f'   ')
        print(f'   下一步操作:')
        print(f'   1. 重启应用程序以使配置生效')
        print(f'   2. 验证应用程序是否正常工作')
        print(f'   3. 如果需要，可以删除实例目录中的旧数据库文件')
        print(f'   ')
        print(f'   注意事项:')
        print(f'   - 重启前请确保没有重要操作正在进行')
        print(f'   - 建议先备份两个数据库文件')
        print(f'   - 如果根目录数据库数据更完整，应该使用根目录的')
        
        print(f'\n=== 验证完成 ===')

if __name__ == '__main__':
    verify_database_config()
