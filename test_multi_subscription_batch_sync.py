#!/usr/bin/env python3
"""
测试多订阅批量同步功能的效果
"""
from app import create_app
from models import db, Subscription, Order, XUIPanelGroup, XUIPanel, User, Product, OrderStatus
from services.subscription_sync_service import SubscriptionSyncService
from utils.order_service import OrderService
import uuid
import time
from datetime import datetime, timedelta

def create_test_subscriptions(count=5):
    """创建测试订阅"""
    print(f'创建 {count} 个测试订阅...')
    
    order_service = OrderService()
    created_orders = []
    
    # 获取产品
    product = Product.query.first()
    if not product:
        print('❌ 没有找到产品，无法创建测试订阅')
        return []
    
    for i in range(count):
        test_email = f'batch_test_{uuid.uuid4().hex[:8]}@example.com'
        test_name = f'批量测试用户{i+1}'
        
        try:
            # 创建订单
            success, order = order_service.create_order(
                customer_email=test_email,
                customer_name=test_name,
                node_type=product.node_type.value,
                duration_days=product.duration_days,
                traffic_limit_gb=product.traffic_limit_gb,
                price=0,
                test_mode=True
            )
            
            if success and order:
                # 关联产品
                order.product_id = product.id
                
                # 处理订单
                process_success, process_message = order_service.process_order(order.order_id)
                if process_success:
                    created_orders.append(order)
                    print(f'  ✓ 创建订单 {i+1}: {order.order_id}')
                else:
                    print(f'  ❌ 处理订单 {i+1} 失败: {process_message}')
            else:
                print(f'  ❌ 创建订单 {i+1} 失败')
                
        except Exception as e:
            print(f'  ❌ 创建订单 {i+1} 异常: {e}')
    
    db.session.commit()
    print(f'✓ 成功创建 {len(created_orders)} 个测试订单')
    return created_orders

def test_batch_sync_performance():
    """测试批量同步性能"""
    app = create_app()
    with app.app_context():
        print('=== 多订阅批量同步功能测试 ===\n')
        
        # 1. 检查系统状态
        print('1. 系统状态检查:')
        
        groups = XUIPanelGroup.query.all()
        if not groups:
            print('❌ 没有面板分组，无法测试')
            return
        
        test_group = groups[0]
        panels = test_group.panels
        print(f'   测试分组: {test_group.name}')
        print(f'   包含面板: {len(panels)} 个')
        
        if not panels:
            print('❌ 分组中没有面板，无法测试')
            return
        
        for panel in panels:
            print(f'     - {panel.name} ({panel.base_url})')
        
        # 2. 检查现有订阅
        existing_subscriptions = Subscription.query.filter_by(is_active=True).all()
        print(f'\n2. 现有活跃订阅: {len(existing_subscriptions)} 个')
        
        # 3. 创建额外的测试订阅
        print(f'\n3. 创建测试订阅:')
        test_orders = create_test_subscriptions(count=8)  # 创建8个测试订阅
        
        if not test_orders:
            print('❌ 无法创建测试订阅')
            return
        
        # 4. 获取所有活跃订阅（包括新创建的）
        all_subscriptions = Subscription.query.filter_by(is_active=True).all()
        print(f'\n4. 总活跃订阅数: {len(all_subscriptions)} 个')
        
        # 5. 测试批量同步性能
        print(f'\n5. 开始批量同步性能测试:')
        print(f'   订阅数量: {len(all_subscriptions)}')
        print(f'   面板数量: {len(panels)}')
        print(f'   理论节点数: {len(all_subscriptions)} × {len(panels)} = {len(all_subscriptions) * len(panels)}')
        
        sync_service = SubscriptionSyncService()
        
        # 记录开始时间
        start_time = time.time()
        print(f'\n   开始时间: {datetime.now().strftime("%H:%M:%S")}')
        
        # 执行批量同步
        result = sync_service.sync_group_all_panels(test_group.id)
        
        # 记录结束时间
        end_time = time.time()
        duration = end_time - start_time
        print(f'   结束时间: {datetime.now().strftime("%H:%M:%S")}')
        print(f'   总耗时: {duration:.2f} 秒')
        
        # 6. 分析同步结果
        print(f'\n6. 批量同步结果分析:')
        print(f'   同步成功: {result["success"]}')
        print(f'   分组名称: {result.get("group_name", "未知")}')
        print(f'   影响订阅: {result.get("affected_subscriptions", 0)} 个')
        print(f'   面板数量: {result.get("panel_count", 0)} 个')
        print(f'   成功数量: {result.get("success_count", 0)} 个')
        print(f'   失败数量: {result.get("failed_count", 0)} 个')
        
        # 计算性能指标
        total_operations = len(all_subscriptions) * len(panels)
        if duration > 0:
            ops_per_second = total_operations / duration
            print(f'\n   性能指标:')
            print(f'     总操作数: {total_operations}')
            print(f'     操作速度: {ops_per_second:.2f} 操作/秒')
            print(f'     平均每个订阅: {duration/len(all_subscriptions):.3f} 秒')
            print(f'     平均每个面板: {duration/len(panels):.3f} 秒')
        
        # 详细面板结果
        panel_results = result.get('panel_results', [])
        if panel_results:
            print(f'\n   面板详细结果:')
            for panel_result in panel_results:
                panel_name = panel_result.get('panel_name', '未知')
                success_count = panel_result.get('success_count', 0)
                failed_count = panel_result.get('failed_count', 0)
                panel_success = panel_result.get('success', False)
                
                status_icon = '✓' if panel_success else '❌'
                print(f'     {status_icon} {panel_name}: 成功 {success_count}, 失败 {failed_count}')
                
                if panel_result.get('error'):
                    print(f'         错误: {panel_result["error"]}')
                if panel_result.get('message'):
                    print(f'         消息: {panel_result["message"]}')
        
        # 7. 验证同步效果
        print(f'\n7. 验证同步效果:')
        
        # 检查数据库中的节点配置
        from models import NodeConfig
        total_node_configs = NodeConfig.query.count()
        active_node_configs = NodeConfig.query.filter_by(is_active=True).count()
        
        print(f'   数据库节点配置总数: {total_node_configs}')
        print(f'   活跃节点配置数: {active_node_configs}')
        
        # 按面板统计节点
        for panel in panels:
            server_address = panel.base_url.replace('http://', '').replace('https://', '').split(':')[0]
            panel_nodes = NodeConfig.query.filter(
                NodeConfig.server_address == server_address,
                NodeConfig.is_active == True
            ).count()
            print(f'     面板 {panel.name}: {panel_nodes} 个节点')
        
        # 8. 测试重复同步检测
        print(f'\n8. 测试重复同步检测:')
        print('   再次执行批量同步，验证重复检测...')
        
        start_time2 = time.time()
        result2 = sync_service.sync_group_all_panels(test_group.id)
        end_time2 = time.time()
        duration2 = end_time2 - start_time2
        
        print(f'   第二次同步耗时: {duration2:.2f} 秒')
        print(f'   第二次同步结果: 成功 {result2.get("success_count", 0)}, 失败 {result2.get("failed_count", 0)}')
        print(f'   重复检测效果: {"✓ 正常" if duration2 < duration else "⚠ 可能有问题"}')
        
        # 9. 性能对比分析
        print(f'\n9. 性能对比分析:')
        print(f'   如果使用单个同步方式:')
        estimated_single_time = total_operations * 0.5  # 假设每个操作0.5秒
        print(f'     预估耗时: {estimated_single_time:.2f} 秒')
        print(f'     实际耗时: {duration:.2f} 秒')
        if estimated_single_time > 0:
            improvement = (estimated_single_time - duration) / estimated_single_time * 100
            print(f'     性能提升: {improvement:.1f}%')
        
        print(f'\n=== 测试完成 ===')
        
        # 清理测试数据（可选）
        cleanup = input('\n是否清理测试数据？(y/N): ').lower().strip()
        if cleanup == 'y':
            print('清理测试数据...')
            for order in test_orders:
                # 删除关联的订阅和节点配置
                if order.subscription:
                    db.session.delete(order.subscription)
                for node_config in order.node_configs:
                    db.session.delete(node_config)
                db.session.delete(order)
            db.session.commit()
            print('✓ 测试数据已清理')

if __name__ == '__main__':
    test_batch_sync_performance()
