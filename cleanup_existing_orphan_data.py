#!/usr/bin/env python3
"""
清理现有数据库中的孤儿数据
"""
from app import create_app
from models import db, Subscription, Order, NodeConfig, TrafficStats, XUIPanel, XUIPanelGroupMembership
from services.data_cleanup_service import data_cleanup_service
from sqlalchemy import text
import os

def cleanup_existing_orphan_data():
    """清理现有数据库中的孤儿数据"""
    app = create_app()
    with app.app_context():
        print('=== 清理现有数据库孤儿数据 ===\n')
        
        # 1. 检查数据库文件
        print('1. 检查数据库文件:')
        
        # 获取数据库文件路径
        db_path = app.config.get('SQLALCHEMY_DATABASE_URI', '').replace('sqlite:///', '')
        if os.path.exists(db_path):
            db_size = os.path.getsize(db_path) / (1024 * 1024)  # MB
            print(f'   数据库文件: {db_path}')
            print(f'   文件大小: {db_size:.2f} MB')
        else:
            print(f'   数据库文件未找到: {db_path}')
        
        # 2. 清理前的数据统计
        print(f'\n2. 清理前的数据统计:')
        
        total_subscriptions = Subscription.query.count()
        active_subscriptions = Subscription.query.filter_by(is_active=True).count()
        total_orders = Order.query.count()
        total_node_configs = NodeConfig.query.count()
        active_node_configs = NodeConfig.query.filter_by(is_active=True).count()
        total_traffic_stats = TrafficStats.query.count()
        
        print(f'   总订阅数: {total_subscriptions} (活跃: {active_subscriptions})')
        print(f'   总订单数: {total_orders}')
        print(f'   总节点配置: {total_node_configs} (活跃: {active_node_configs})')
        print(f'   总流量统计: {total_traffic_stats}')
        
        # 3. 识别孤儿数据
        print(f'\n3. 识别孤儿数据:')
        
        # 3.1 孤儿流量统计
        active_subscription_ids = [sub.id for sub in Subscription.query.filter_by(is_active=True).all()]
        print(f'   活跃订阅ID: {active_subscription_ids}')
        
        if active_subscription_ids:
            orphaned_traffic_stats = TrafficStats.query.filter(
                ~TrafficStats.subscription_id.in_(active_subscription_ids)
            ).all()
        else:
            orphaned_traffic_stats = TrafficStats.query.all()
        
        print(f'   孤儿流量统计: {len(orphaned_traffic_stats)} 条')
        
        # 3.2 孤儿节点配置
        # 获取所有面板的服务器地址
        panel_addresses = set()
        try:
            # 使用原生SQL避免枚举问题
            result = db.session.execute(text("SELECT base_url FROM xui_panel"))
            for row in result:
                base_url = row[0]
                server_address = base_url.replace('http://', '').replace('https://', '').split(':')[0]
                panel_addresses.add(server_address)
        except Exception as e:
            print(f'   查询面板地址失败: {e}')
            # 如果查询失败，假设没有活跃面板
            panel_addresses = set()
        
        print(f'   面板服务器地址: {list(panel_addresses)}')
        
        orphaned_node_configs = []
        for config in NodeConfig.query.filter_by(is_active=True).all():
            if config.server_address not in panel_addresses:
                orphaned_node_configs.append(config)
        
        print(f'   孤儿节点配置: {len(orphaned_node_configs)} 个')
        
        # 3.3 孤儿分组成员关系
        try:
            # 查找引用不存在面板的分组成员关系
            result = db.session.execute(text("""
                SELECT gm.id, gm.panel_id, gm.group_id 
                FROM xui_panel_group_membership gm 
                LEFT JOIN xui_panel p ON gm.panel_id = p.id 
                WHERE p.id IS NULL
            """))
            orphaned_memberships = list(result)
            print(f'   孤儿分组成员关系: {len(orphaned_memberships)} 个')
        except Exception as e:
            print(f'   查询孤儿分组成员关系失败: {e}')
            orphaned_memberships = []
        
        # 4. 执行清理
        print(f'\n4. 执行数据清理:')
        
        total_cleaned = 0
        
        # 4.1 清理孤儿流量统计
        if orphaned_traffic_stats:
            print(f'\n   清理孤儿流量统计数据:')
            
            total_traffic_mb = 0
            for stat in orphaned_traffic_stats:
                traffic_mb = (stat.upload_bytes + stat.download_bytes) / (1024**2) if stat.upload_bytes and stat.download_bytes else 0
                total_traffic_mb += traffic_mb
                print(f'     删除流量统计 {stat.id}: 订阅ID {stat.subscription_id}, 流量 {traffic_mb:.2f}MB')
                db.session.delete(stat)
            
            print(f'   ✓ 清理了 {len(orphaned_traffic_stats)} 条孤儿流量统计，释放 {total_traffic_mb:.2f}MB 数据')
            total_cleaned += len(orphaned_traffic_stats)
        
        # 4.2 清理孤儿节点配置
        if orphaned_node_configs:
            print(f'\n   清理孤儿节点配置:')
            
            for config in orphaned_node_configs:
                print(f'     停用节点配置 {config.id}: 服务器 {config.server_address}, 邮箱 {config.client_email}')
                config.is_active = False
            
            print(f'   ✓ 停用了 {len(orphaned_node_configs)} 个孤儿节点配置')
            total_cleaned += len(orphaned_node_configs)
        
        # 4.3 清理孤儿分组成员关系
        if orphaned_memberships:
            print(f'\n   清理孤儿分组成员关系:')
            
            for membership_data in orphaned_memberships:
                membership_id, panel_id, group_id = membership_data
                print(f'     删除分组成员关系 {membership_id}: 面板ID {panel_id} -> 分组ID {group_id}')
                db.session.execute(text("DELETE FROM xui_panel_group_membership WHERE id = :id"), {"id": membership_id})
            
            print(f'   ✓ 清理了 {len(orphaned_memberships)} 个孤儿分组成员关系')
            total_cleaned += len(orphaned_memberships)
        
        # 5. 提交更改
        if total_cleaned > 0:
            print(f'\n5. 提交数据库更改:')
            try:
                db.session.commit()
                print(f'   ✓ 成功提交所有更改')
            except Exception as e:
                print(f'   ❌ 提交失败: {e}')
                db.session.rollback()
                return
        else:
            print(f'\n5. 无需清理:')
            print(f'   ✓ 数据库中没有发现孤儿数据')
        
        # 6. 清理后的数据统计
        print(f'\n6. 清理后的数据统计:')
        
        final_subscriptions = Subscription.query.count()
        final_active_subscriptions = Subscription.query.filter_by(is_active=True).count()
        final_orders = Order.query.count()
        final_node_configs = NodeConfig.query.count()
        final_active_node_configs = NodeConfig.query.filter_by(is_active=True).count()
        final_traffic_stats = TrafficStats.query.count()
        
        print(f'   总订阅数: {final_subscriptions} (活跃: {final_active_subscriptions})')
        print(f'   总订单数: {final_orders}')
        print(f'   总节点配置: {final_node_configs} (活跃: {final_active_node_configs})')
        print(f'   总流量统计: {final_traffic_stats}')
        
        # 7. 清理效果统计
        print(f'\n7. 清理效果统计:')
        
        print(f'   清理前后对比:')
        print(f'     流量统计: {total_traffic_stats} -> {final_traffic_stats} (减少 {total_traffic_stats - final_traffic_stats})')
        print(f'     活跃节点配置: {active_node_configs} -> {final_active_node_configs} (减少 {active_node_configs - final_active_node_configs})')
        
        print(f'\n   总清理项目: {total_cleaned} 项')
        
        if total_cleaned > 0:
            print(f'   ✅ 孤儿数据清理完成！')
        else:
            print(f'   ✅ 数据库状态良好，无孤儿数据！')
        
        # 8. 验证清理效果
        print(f'\n8. 验证清理效果:')
        
        # 重新检查孤儿数据
        final_active_subscription_ids = [sub.id for sub in Subscription.query.filter_by(is_active=True).all()]
        if final_active_subscription_ids:
            final_orphaned_traffic_stats = TrafficStats.query.filter(
                ~TrafficStats.subscription_id.in_(final_active_subscription_ids)
            ).count()
        else:
            final_orphaned_traffic_stats = TrafficStats.query.count()
        
        final_orphaned_node_configs = 0
        for config in NodeConfig.query.filter_by(is_active=True).all():
            if config.server_address not in panel_addresses:
                final_orphaned_node_configs += 1
        
        print(f'   剩余孤儿流量统计: {final_orphaned_traffic_stats} 条')
        print(f'   剩余孤儿节点配置: {final_orphaned_node_configs} 个')
        
        if final_orphaned_traffic_stats == 0 and final_orphaned_node_configs == 0:
            print(f'   🎉 所有孤儿数据已成功清理！')
        else:
            print(f'   ⚠️ 仍有少量孤儿数据，可能需要进一步检查')
        
        # 9. 数据库文件大小对比
        if os.path.exists(db_path):
            final_db_size = os.path.getsize(db_path) / (1024 * 1024)  # MB
            size_reduction = db_size - final_db_size
            print(f'\n9. 数据库文件大小变化:')
            print(f'   清理前: {db_size:.2f} MB')
            print(f'   清理后: {final_db_size:.2f} MB')
            if size_reduction > 0:
                print(f'   减少: {size_reduction:.2f} MB')
            else:
                print(f'   大小基本无变化')
        
        print(f'\n=== 孤儿数据清理完成 ===')

if __name__ == '__main__':
    cleanup_existing_orphan_data()
