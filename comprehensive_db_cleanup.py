#!/usr/bin/env python3
"""
全面的数据库检查和清理
"""
from app import create_app
from models import db
from sqlalchemy import text, inspect
import os

def comprehensive_db_cleanup():
    """全面的数据库检查和清理"""
    app = create_app()
    with app.app_context():
        print('=== 全面数据库检查和清理 ===\n')
        
        # 1. 检查数据库文件和表结构
        print('1. 检查数据库文件和表结构:')
        
        # 获取数据库文件路径
        db_path = app.config.get('SQLALCHEMY_DATABASE_URI', '').replace('sqlite:///', '')
        if os.path.exists(db_path):
            db_size = os.path.getsize(db_path) / (1024 * 1024)  # MB
            print(f'   数据库文件: {db_path}')
            print(f'   文件大小: {db_size:.2f} MB')
        else:
            print(f'   数据库文件未找到: {db_path}')
            return
        
        # 检查表结构
        inspector = inspect(db.engine)
        tables = inspector.get_table_names()
        print(f'   数据库表数量: {len(tables)}')
        print(f'   现有表: {", ".join(tables)}')
        
        # 2. 检查各表的数据量
        print(f'\n2. 检查各表的数据量:')
        
        table_stats = {}
        for table in tables:
            try:
                result = db.session.execute(text(f"SELECT COUNT(*) FROM {table}"))
                count = result.scalar()
                table_stats[table] = count
                print(f'   {table}: {count} 条记录')
            except Exception as e:
                print(f'   {table}: 查询失败 - {e}')
                table_stats[table] = 0
        
        # 3. 检查关键表的孤儿数据
        print(f'\n3. 检查关键表的孤儿数据:')
        
        orphan_data = {}
        
        # 3.1 检查流量统计表
        if 'traffic_stats' in tables and 'subscription' in tables:
            try:
                result = db.session.execute(text("""
                    SELECT COUNT(*) FROM traffic_stats ts 
                    LEFT JOIN subscription s ON ts.subscription_id = s.id 
                    WHERE s.id IS NULL OR s.is_active = 0
                """))
                orphan_traffic_stats = result.scalar()
                orphan_data['traffic_stats'] = orphan_traffic_stats
                print(f'   孤儿流量统计: {orphan_traffic_stats} 条')
            except Exception as e:
                print(f'   检查孤儿流量统计失败: {e}')
                orphan_data['traffic_stats'] = 0
        
        # 3.2 检查节点配置表
        if 'node_config' in tables:
            try:
                # 检查引用不存在订单的节点配置
                result = db.session.execute(text("""
                    SELECT COUNT(*) FROM node_config nc 
                    LEFT JOIN "order" o ON nc.order_id = o.id 
                    WHERE o.id IS NULL AND nc.is_active = 1
                """))
                orphan_node_configs = result.scalar()
                orphan_data['node_config'] = orphan_node_configs
                print(f'   孤儿节点配置: {orphan_node_configs} 个')
            except Exception as e:
                print(f'   检查孤儿节点配置失败: {e}')
                orphan_data['node_config'] = 0
        
        # 3.3 检查续费任务表
        if 'renewal_task' in tables and 'subscription' in tables:
            try:
                result = db.session.execute(text("""
                    SELECT COUNT(*) FROM renewal_task rt 
                    LEFT JOIN subscription s ON rt.subscription_id = s.id 
                    WHERE s.id IS NULL
                """))
                orphan_renewal_tasks = result.scalar()
                orphan_data['renewal_task'] = orphan_renewal_tasks
                print(f'   孤儿续费任务: {orphan_renewal_tasks} 个')
            except Exception as e:
                print(f'   检查孤儿续费任务失败: {e}')
                orphan_data['renewal_task'] = 0
        
        # 4. 执行清理
        print(f'\n4. 执行数据清理:')
        
        total_cleaned = 0
        
        # 4.1 清理孤儿流量统计
        if orphan_data.get('traffic_stats', 0) > 0:
            print(f'\n   清理孤儿流量统计:')
            try:
                # 获取详细信息
                result = db.session.execute(text("""
                    SELECT ts.id, ts.subscription_id, ts.upload_bytes, ts.download_bytes 
                    FROM traffic_stats ts 
                    LEFT JOIN subscription s ON ts.subscription_id = s.id 
                    WHERE s.id IS NULL OR s.is_active = 0
                    LIMIT 10
                """))
                
                orphan_stats = list(result)
                total_traffic_mb = 0
                
                for stat in orphan_stats:
                    stat_id, sub_id, upload, download = stat
                    traffic_mb = ((upload or 0) + (download or 0)) / (1024**2)
                    total_traffic_mb += traffic_mb
                    print(f'     删除流量统计 {stat_id}: 订阅ID {sub_id}, 流量 {traffic_mb:.2f}MB')
                
                # 执行删除
                deleted_count = db.session.execute(text("""
                    DELETE FROM traffic_stats 
                    WHERE id IN (
                        SELECT ts.id FROM traffic_stats ts 
                        LEFT JOIN subscription s ON ts.subscription_id = s.id 
                        WHERE s.id IS NULL OR s.is_active = 0
                    )
                """)).rowcount
                
                print(f'   ✓ 清理了 {deleted_count} 条孤儿流量统计，释放约 {total_traffic_mb:.2f}MB 数据')
                total_cleaned += deleted_count
                
            except Exception as e:
                print(f'   清理孤儿流量统计失败: {e}')
        
        # 4.2 清理孤儿节点配置
        if orphan_data.get('node_config', 0) > 0:
            print(f'\n   清理孤儿节点配置:')
            try:
                # 获取详细信息
                result = db.session.execute(text("""
                    SELECT nc.id, nc.order_id, nc.server_address, nc.client_email 
                    FROM node_config nc 
                    LEFT JOIN "order" o ON nc.order_id = o.id 
                    WHERE o.id IS NULL AND nc.is_active = 1
                    LIMIT 10
                """))
                
                orphan_configs = list(result)
                
                for config in orphan_configs:
                    config_id, order_id, server, email = config
                    print(f'     停用节点配置 {config_id}: 订单ID {order_id}, 服务器 {server}')
                
                # 执行停用
                updated_count = db.session.execute(text("""
                    UPDATE node_config SET is_active = 0 
                    WHERE id IN (
                        SELECT nc.id FROM node_config nc 
                        LEFT JOIN "order" o ON nc.order_id = o.id 
                        WHERE o.id IS NULL AND nc.is_active = 1
                    )
                """)).rowcount
                
                print(f'   ✓ 停用了 {updated_count} 个孤儿节点配置')
                total_cleaned += updated_count
                
            except Exception as e:
                print(f'   清理孤儿节点配置失败: {e}')
        
        # 4.3 清理孤儿续费任务
        if orphan_data.get('renewal_task', 0) > 0:
            print(f'\n   清理孤儿续费任务:')
            try:
                # 执行删除
                deleted_count = db.session.execute(text("""
                    DELETE FROM renewal_task 
                    WHERE id IN (
                        SELECT rt.id FROM renewal_task rt 
                        LEFT JOIN subscription s ON rt.subscription_id = s.id 
                        WHERE s.id IS NULL
                    )
                """)).rowcount
                
                print(f'   ✓ 清理了 {deleted_count} 个孤儿续费任务')
                total_cleaned += deleted_count
                
            except Exception as e:
                print(f'   清理孤儿续费任务失败: {e}')
        
        # 5. 提交更改
        if total_cleaned > 0:
            print(f'\n5. 提交数据库更改:')
            try:
                db.session.commit()
                print(f'   ✓ 成功提交所有更改')
            except Exception as e:
                print(f'   ❌ 提交失败: {e}')
                db.session.rollback()
                return
        else:
            print(f'\n5. 无需清理:')
            print(f'   ✓ 数据库中没有发现孤儿数据')
        
        # 6. 清理后的统计
        print(f'\n6. 清理后的统计:')
        
        for table in tables:
            try:
                result = db.session.execute(text(f"SELECT COUNT(*) FROM {table}"))
                new_count = result.scalar()
                old_count = table_stats.get(table, 0)
                change = old_count - new_count
                
                if change > 0:
                    print(f'   {table}: {old_count} -> {new_count} (减少 {change})')
                else:
                    print(f'   {table}: {new_count} 条记录')
            except Exception as e:
                print(f'   {table}: 查询失败 - {e}')
        
        # 7. 数据库优化
        print(f'\n7. 数据库优化:')
        
        try:
            # 执行VACUUM来压缩数据库
            db.session.execute(text("VACUUM"))
            print(f'   ✓ 执行数据库压缩 (VACUUM)')
        except Exception as e:
            print(f'   数据库压缩失败: {e}')
        
        # 8. 最终状态
        print(f'\n8. 最终状态:')
        
        if os.path.exists(db_path):
            final_db_size = os.path.getsize(db_path) / (1024 * 1024)  # MB
            size_change = db_size - final_db_size
            print(f'   数据库文件大小: {db_size:.2f} MB -> {final_db_size:.2f} MB')
            if size_change > 0:
                print(f'   减少: {size_change:.2f} MB')
            elif size_change < 0:
                print(f'   增加: {abs(size_change):.2f} MB')
            else:
                print(f'   大小无变化')
        
        print(f'\n   总清理项目: {total_cleaned} 项')
        
        if total_cleaned > 0:
            print(f'   🎉 数据库清理完成！')
        else:
            print(f'   ✅ 数据库状态良好！')
        
        print(f'\n=== 全面数据库清理完成 ===')

if __name__ == '__main__':
    comprehensive_db_cleanup()
