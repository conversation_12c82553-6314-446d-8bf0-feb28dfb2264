{% extends "base.html" %}

{% block title %}协议模板管理 - 管理后台{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>
                    <i class="bi bi-file-code"></i> 协议模板管理
                </h1>
                <div>
                    <a href="{{ url_for('protocol_template.create_template') }}" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> 创建模板
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 协议模板列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-list"></i> 协议模板列表</h5>
                </div>
                <div class="card-body">
                    {% if templates %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>模板名称</th>
                                    <th>协议类型</th>
                                    <th>描述</th>
                                    <th>状态</th>
                                    <th>类型</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for template in templates %}
                                <tr>
                                    <td>
                                        <strong>{{ template.name }}</strong>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 'primary' if template.protocol_type.value == 'vless' else 'success' if template.protocol_type.value == 'vmess' else 'warning' }}">
                                            {{ template.protocol_type_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ template.description or '无描述' }}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if template.is_active else 'secondary' }}">
                                            {{ '启用' if template.is_active else '禁用' }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if template.is_default %}
                                            <span class="badge bg-info">默认</span>
                                        {% else %}
                                            <span class="badge bg-light text-dark">自定义</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ template.created_at.strftime('%Y-%m-%d %H:%M') if template.created_at else '-' }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <button type="button" class="btn btn-outline-info" onclick="previewTemplate({{ template.id }})">
                                                <i class="bi bi-eye"></i> 预览
                                            </button>
                                            <a href="{{ url_for('protocol_template.edit_template', template_id=template.id) }}" class="btn btn-outline-primary">
                                                <i class="bi bi-pencil"></i> 编辑
                                            </a>
                                            <button type="button" class="btn btn-outline-danger" onclick="deleteTemplate({{ template.id }}, '{{ template.name }}', {{ 'true' if template.is_default else 'false' }})">
                                                <i class="bi bi-trash"></i> 删除
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="bi bi-file-code text-muted" style="font-size: 4rem;"></i>
                        <h4 class="text-muted mt-3">暂无协议模板</h4>
                        <p class="text-muted">还没有创建任何协议模板</p>
                        <a href="{{ url_for('protocol_template.create_template') }}" class="btn btn-primary">
                            <i class="bi bi-plus-circle"></i> 创建第一个模板
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- 返回按钮 -->
    <div class="row mt-4">
        <div class="col-12">
            <a href="{{ url_for('admin.dashboard') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回仪表板
            </a>
        </div>
    </div>
</div>

<!-- 预览模态框 -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">协议模板预览</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <pre id="previewContent" class="bg-light p-3 rounded"></pre>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除协议模板 "<span id="deleteTemplateName"></span>" 吗？</p>
                <div id="defaultTemplateWarning" class="alert alert-warning" style="display: none;">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>注意：</strong>这是系统默认模板，删除后可能影响现有功能。
                </div>
                <p class="text-danger">此操作不可撤销！</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function previewTemplate(templateId) {
    fetch(`/admin/api/protocol-templates/${templateId}/preview`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('previewContent').textContent = data.preview;
            new bootstrap.Modal(document.getElementById('previewModal')).show();
        } else {
            alert('预览失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('预览失败');
    });
}

function deleteTemplate(templateId, templateName, isDefault) {
    document.getElementById('deleteTemplateName').textContent = templateName;
    document.getElementById('deleteForm').action = `/admin/protocol-templates/${templateId}/delete`;

    // 显示/隐藏默认模板警告
    const warningDiv = document.getElementById('defaultTemplateWarning');
    if (isDefault) {
        warningDiv.style.display = 'block';
    } else {
        warningDiv.style.display = 'none';
    }

    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>

{% endblock %}
