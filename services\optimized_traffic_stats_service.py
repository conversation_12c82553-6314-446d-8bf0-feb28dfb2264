"""
优化的流量统计服务
使用双表设计，解决多订阅性能问题
"""
from models import db
from models.optimized_traffic import CurrentTrafficStats, HistoricalTrafficStats, AggregationType
from models import Subscription, XUIPanel, NodeConfig
from datetime import datetime, timedelta
from sqlalchemy import func
from sqlalchemy.dialects.sqlite import insert
import logging

logger = logging.getLogger(__name__)

class OptimizedTrafficStatsService:
    """优化的流量统计服务"""
    
    def __init__(self):
        self.xui_service = None
    
    def collect_all_traffic_stats(self):
        """
        收集所有订阅的流量统计（优化版本）
        使用UPSERT操作，每个订阅只维护一条记录
        """
        try:
            logger.info("开始收集流量统计（优化版本）...")
            start_time = datetime.now()
            
            # 获取所有活跃订阅
            active_subscriptions = Subscription.query.filter_by(is_active=True).all()
            
            if not active_subscriptions:
                logger.info("没有活跃订阅，跳过流量统计收集")
                return True
            
            logger.info(f"找到 {len(active_subscriptions)} 个活跃订阅")
            
            # 按分组批量处理
            processed_count = 0
            updated_count = 0
            
            for subscription in active_subscriptions:
                try:
                    # 收集单个订阅的流量
                    success, traffic_data = self._collect_subscription_traffic(subscription)
                    
                    if success and traffic_data:
                        # 使用UPSERT更新实时流量表
                        self._upsert_current_traffic_stats(subscription.id, traffic_data)
                        updated_count += 1
                    
                    processed_count += 1
                    
                except Exception as e:
                    logger.error(f"处理订阅 {subscription.id} 流量统计失败: {e}")
                    continue
            
            # 提交事务
            db.session.commit()
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            logger.info(f"流量统计收集完成：处理 {processed_count} 个订阅，更新 {updated_count} 条记录，耗时 {duration:.2f}秒")
            
            return True
            
        except Exception as e:
            logger.error(f"收集流量统计失败: {e}")
            db.session.rollback()
            return False
    
    def _collect_subscription_traffic(self, subscription):
        """收集单个订阅的流量数据"""
        try:
            if not subscription.order or not subscription.order.node_configs:
                return False, None
            
            total_upload = 0
            total_download = 0
            
            # 从XUI面板收集流量数据
            for node_config in subscription.order.node_configs:
                if not node_config.is_active:
                    continue
                
                try:
                    # 这里调用XUI API获取流量数据
                    # 具体实现依赖于现有的XUI服务
                    upload, download = self._get_traffic_from_xui(node_config)
                    total_upload += upload
                    total_download += download
                    
                except Exception as e:
                    logger.warning(f"获取节点 {node_config.id} 流量失败: {e}")
                    continue
            
            return True, {
                'upload_bytes': total_upload,
                'download_bytes': total_download,
                'total_bytes': total_upload + total_download
            }
            
        except Exception as e:
            logger.error(f"收集订阅 {subscription.id} 流量失败: {e}")
            return False, None
    
    def _get_traffic_from_xui(self, node_config):
        """从XUI面板获取流量数据"""
        # 这里应该调用现有的XUI服务
        # 暂时返回模拟数据
        return 0, 0
    
    def _upsert_current_traffic_stats(self, subscription_id, traffic_data):
        """
        使用UPSERT操作更新实时流量统计
        如果记录存在则更新，不存在则插入
        """
        try:
            # SQLite的UPSERT语法
            stmt = insert(CurrentTrafficStats).values(
                subscription_id=subscription_id,
                upload_bytes=traffic_data['upload_bytes'],
                download_bytes=traffic_data['download_bytes'],
                total_bytes=traffic_data['total_bytes'],
                last_updated=datetime.utcnow()
            )
            
            # 如果记录已存在，则更新
            stmt = stmt.on_conflict_do_update(
                index_elements=['subscription_id'],
                set_=dict(
                    upload_bytes=stmt.excluded.upload_bytes,
                    download_bytes=stmt.excluded.download_bytes,
                    total_bytes=stmt.excluded.total_bytes,
                    last_updated=stmt.excluded.last_updated
                )
            )
            
            db.session.execute(stmt)
            
        except Exception as e:
            logger.error(f"更新订阅 {subscription_id} 实时流量统计失败: {e}")
            raise
    
    def aggregate_historical_data(self, aggregation_type=AggregationType.HOURLY):
        """
        聚合历史数据
        将实时数据聚合到历史表中
        """
        try:
            logger.info(f"开始聚合历史数据：{aggregation_type.value}")
            
            # 确定聚合时间范围
            now = datetime.utcnow()
            if aggregation_type == AggregationType.HOURLY:
                # 聚合上一个小时的数据
                period_end = now.replace(minute=0, second=0, microsecond=0)
                period_start = period_end - timedelta(hours=1)
            elif aggregation_type == AggregationType.DAILY:
                # 聚合昨天的数据
                period_end = now.replace(hour=0, minute=0, second=0, microsecond=0)
                period_start = period_end - timedelta(days=1)
            else:
                logger.warning(f"暂不支持的聚合类型: {aggregation_type}")
                return False
            
            # 获取所有当前流量统计
            current_stats = CurrentTrafficStats.query.all()
            
            aggregated_count = 0
            for stat in current_stats:
                try:
                    # 检查是否已经聚合过
                    existing = HistoricalTrafficStats.query.filter_by(
                        subscription_id=stat.subscription_id,
                        period_start=period_start,
                        period_end=period_end,
                        aggregation_type=aggregation_type
                    ).first()
                    
                    if existing:
                        continue  # 已经聚合过，跳过
                    
                    # 创建历史记录
                    historical_stat = HistoricalTrafficStats(
                        subscription_id=stat.subscription_id,
                        upload_bytes=stat.upload_bytes,
                        download_bytes=stat.download_bytes,
                        total_bytes=stat.total_bytes,
                        period_start=period_start,
                        period_end=period_end,
                        aggregation_type=aggregation_type
                    )
                    
                    db.session.add(historical_stat)
                    aggregated_count += 1
                    
                except Exception as e:
                    logger.error(f"聚合订阅 {stat.subscription_id} 历史数据失败: {e}")
                    continue
            
            db.session.commit()
            
            logger.info(f"历史数据聚合完成：聚合了 {aggregated_count} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"聚合历史数据失败: {e}")
            db.session.rollback()
            return False
    
    def get_current_traffic(self, subscription_id):
        """获取订阅的当前流量统计"""
        try:
            stat = CurrentTrafficStats.query.filter_by(
                subscription_id=subscription_id
            ).first()
            
            if stat:
                return {
                    'subscription_id': stat.subscription_id,
                    'upload_bytes': stat.upload_bytes,
                    'download_bytes': stat.download_bytes,
                    'total_bytes': stat.total_bytes,
                    'upload_gb': stat.upload_gb,
                    'download_gb': stat.download_gb,
                    'total_gb': stat.total_gb,
                    'last_updated': stat.last_updated
                }
            else:
                return None
                
        except Exception as e:
            logger.error(f"获取订阅 {subscription_id} 当前流量失败: {e}")
            return None
    
    def get_historical_traffic(self, subscription_id, start_date=None, end_date=None, aggregation_type=AggregationType.HOURLY):
        """获取订阅的历史流量统计"""
        try:
            query = HistoricalTrafficStats.query.filter_by(
                subscription_id=subscription_id,
                aggregation_type=aggregation_type
            )
            
            if start_date:
                query = query.filter(HistoricalTrafficStats.period_start >= start_date)
            
            if end_date:
                query = query.filter(HistoricalTrafficStats.period_end <= end_date)
            
            stats = query.order_by(HistoricalTrafficStats.period_start).all()
            
            return [{
                'subscription_id': stat.subscription_id,
                'upload_bytes': stat.upload_bytes,
                'download_bytes': stat.download_bytes,
                'total_bytes': stat.total_bytes,
                'upload_gb': stat.upload_gb,
                'download_gb': stat.download_gb,
                'total_gb': stat.total_gb,
                'period_start': stat.period_start,
                'period_end': stat.period_end,
                'aggregation_type': stat.aggregation_type.value
            } for stat in stats]
            
        except Exception as e:
            logger.error(f"获取订阅 {subscription_id} 历史流量失败: {e}")
            return []
    
    def cleanup_old_historical_data(self, days=90):
        """清理旧的历史数据"""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            
            deleted_count = HistoricalTrafficStats.query.filter(
                HistoricalTrafficStats.period_end < cutoff_date
            ).delete()
            
            db.session.commit()
            
            logger.info(f"清理了 {deleted_count} 条旧历史数据")
            return True
            
        except Exception as e:
            logger.error(f"清理旧历史数据失败: {e}")
            db.session.rollback()
            return False

# 创建全局实例
optimized_traffic_stats_service = OptimizedTrafficStatsService()
