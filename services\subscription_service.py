"""
订阅服务 - 处理用户订阅链接和配置生成
"""
import base64
import json
import logging
from typing import List, Dict, Optional, Tuple
from datetime import datetime, timezone
from models import db, User, Order, NodeConfig, OrderStatus, Subscription, TrafficStats, ProtocolTemplate, XUIPanelGroupMembership
# MultiXUIManager导入放在需要时进行，避免循环导入
from sqlalchemy import or_

logger = logging.getLogger(__name__)

class SubscriptionService:
    """订阅服务类"""
    
    def __init__(self):
        self.xui_manager = None

    def handle_user_subscription_override(self, user_id: int, new_order: Order) -> bool:
        """
        处理用户订阅覆盖逻辑
        一个用户只能有一个活跃订阅，新订阅会覆盖旧订阅
        新版本：彻底删除旧订阅（包括X-UI面板客户端和数据库记录）

        Args:
            user_id: 用户ID
            new_order: 新订单

        Returns:
            bool: 处理是否成功
        """
        try:
            # 查找用户当前的活跃订阅
            # 明确指定连接条件以避免多外键关系的歧义
            existing_subscriptions = Subscription.query.join(
                Order, Subscription.order_id == Order.id
            ).filter(
                Order.user_id == user_id,
                Subscription.is_active == True,
                Subscription.expires_at > datetime.now(timezone.utc)
            ).all()

            if existing_subscriptions:
                logger.info(f"用户 {user_id} 有 {len(existing_subscriptions)} 个活跃订阅，将被新订阅彻底删除")

                # 彻底删除所有旧订阅
                for old_subscription in existing_subscriptions:
                    # 记录删除日志
                    logger.info(f"开始删除订阅 {old_subscription.id}，关联订单: {old_subscription.order.order_id}")

                    # 从X-UI面板删除客户端
                    deleted_clients, failed_deletions = self.delete_subscription_clients_from_xui(old_subscription)
                    
                    if deleted_clients:
                        logger.info(f"成功从X-UI面板删除 {len(deleted_clients)} 个客户端: {deleted_clients}")
                    
                    if failed_deletions:
                        logger.warning(f"删除失败的客户端: {failed_deletions}")

                    # 删除订阅和关联的订单、节点配置
                    self.delete_subscription_and_order(old_subscription)

                db.session.commit()
                logger.info(f"成功删除用户 {user_id} 的所有旧订阅")

            return True

        except Exception as e:
            logger.error(f"处理用户订阅覆盖失败 user_id={user_id}: {e}")
            db.session.rollback()
            return False

    def delete_subscription_clients_from_xui(self, subscription: Subscription) -> Tuple[List[str], List[str]]:
        """
        从X-UI面板删除订阅关联的所有客户端

        Args:
            subscription: 订阅对象

        Returns:
            Tuple[List[str], List[str]]: (删除成功的客户端列表, 删除失败的客户端列表)
        """
        deleted_clients = []
        failed_deletions = []

        try:
            order = subscription.order
            if not order or not order.node_configs:
                logger.info(f"订阅 {subscription.id} 没有关联的节点配置")
                return deleted_clients, failed_deletions

            # 获取订阅关联的分组
            group = subscription.group
            if group:
                # 使用分组中的面板删除客户端
                logger.info(f"使用订阅关联分组 {group.name} 删除客户端")
                panels = group.active_panels
                deleted_clients, failed_deletions = self._delete_clients_from_panels(
                    order.node_configs, panels, f"分组 {group.name}"
                )
            else:
                # 如果没有关联分组，尝试从所有活跃面板删除客户端
                logger.warning(f"订阅 {subscription.id} 没有关联分组，尝试从所有活跃面板删除客户端")
                from models import XUIPanel, PanelStatus
                all_panels = XUIPanel.query.filter_by(status=PanelStatus.ACTIVE).all()
                deleted_clients, failed_deletions = self._delete_clients_from_panels(
                    order.node_configs, all_panels, "所有活跃面板"
                )

            return deleted_clients, failed_deletions

        except Exception as e:
            logger.error(f"从X-UI面板删除订阅 {subscription.id} 的客户端失败: {e}")
            return deleted_clients, failed_deletions

    def _delete_clients_from_panels(self, node_configs: List, panels: List, panel_source: str) -> Tuple[List[str], List[str]]:
        """
        从指定面板列表中删除客户端

        Args:
            node_configs: 节点配置列表
            panels: 面板列表
            panel_source: 面板来源描述（用于日志）

        Returns:
            Tuple[List[str], List[str]]: (删除成功的客户端列表, 删除失败的客户端列表)
        """
        deleted_clients = []
        failed_deletions = []

        logger.info(f"从{panel_source}删除客户端，包含 {len(panels)} 个面板")

        for node_config in node_configs:
            if not node_config.is_active or not node_config.client_email:
                continue

            # 尝试从面板中删除客户端
            client_deleted = False
            for panel in panels:
                try:
                    # 创建XUI客户端
                    from xui_client import XUIClient
                    client = XUIClient(
                        base_url=panel.base_url,
                        username=panel.username,
                        password=panel.password,
                        path_prefix=panel.path_prefix
                    )

                    # 尝试从X-UI面板删除客户端
                    success, error_msg = client.delete_client_by_email(node_config.client_email)
                    if success:
                        deleted_clients.append(f"{node_config.client_email} (面板: {panel.name})")
                        logger.info(f"成功从面板 {panel.name} 删除客户端: {node_config.client_email}")
                        client_deleted = True
                        break  # 成功删除后跳出循环
                    else:
                        logger.warning(f"从面板 {panel.name} 删除客户端失败: {node_config.client_email} - {error_msg}")

                except Exception as e:
                    logger.error(f"从面板 {panel.name} 删除客户端 {node_config.client_email} 时发生错误: {e}")

            if not client_deleted:
                failed_deletions.append(f"{node_config.client_email}: 在{panel_source}中都删除失败")

        return deleted_clients, failed_deletions

    def delete_subscription_and_order(self, subscription: Subscription) -> None:
        """
        从数据库中删除订阅及其关联的订单和节点配置

        Args:
            subscription: 要删除的订阅对象
        """
        try:
            # {{CHENGQI:
            # Action: Added
            # Timestamp: 2025-01-27 15:05:00 +08:00
            # Task_ID: P4-LD-005
            # Principle_Applied: SOLID - 集成流量基准删除策略，支持软删除和硬删除
            # Language: Python
            # Description: 在删除订阅时根据配置策略处理流量基准数据
            # }}

            # 处理流量基准数据删除
            from services.traffic_baseline_deletion_service import traffic_baseline_deletion_service
            baseline_result = traffic_baseline_deletion_service.handle_subscription_deletion(subscription.id)

            if baseline_result['success']:
                logger.info(f"流量基准处理成功: {baseline_result.get('message', '')}")
            else:
                logger.warning(f"流量基准处理失败: {baseline_result.get('error', '')}")

            order = subscription.order
            if order:
                logger.info(f"删除订单 {order.order_id} 及其关联数据")

                # 删除节点配置（由于外键关系，这些会自动级联删除）
                node_config_count = len(order.node_configs)
                
                # 先手动删除关联的流量统计记录
                from models import TrafficStats
                traffic_stats_count = TrafficStats.query.filter_by(subscription_id=subscription.id).count()
                if traffic_stats_count > 0:
                    TrafficStats.query.filter_by(subscription_id=subscription.id).delete()
                    logger.info(f"删除了 {traffic_stats_count} 条关联的流量统计记录")

                # 先手动删除关联的续费任务记录
                from models import RenewalTask
                renewal_tasks_count = RenewalTask.query.filter_by(subscription_id=subscription.id).count()
                if renewal_tasks_count > 0:
                    RenewalTask.query.filter_by(subscription_id=subscription.id).delete()
                    logger.info(f"删除了 {renewal_tasks_count} 条关联的续费任务记录")
                
                # 删除订阅记录
                db.session.delete(subscription)
                
                # 删除订单记录（这会级联删除节点配置）
                db.session.delete(order)
                
                logger.info(f"已标记删除订单 {order.order_id}、订阅 {subscription.id}、{node_config_count} 个节点配置和 {traffic_stats_count} 条流量统计记录")
            else:
                # 如果没有关联订单，只删除订阅和相关记录
                from models import TrafficStats, RenewalTask
                traffic_stats_count = TrafficStats.query.filter_by(subscription_id=subscription.id).count()
                if traffic_stats_count > 0:
                    TrafficStats.query.filter_by(subscription_id=subscription.id).delete()
                    logger.info(f"删除了 {traffic_stats_count} 条关联的流量统计记录")

                renewal_tasks_count = RenewalTask.query.filter_by(subscription_id=subscription.id).count()
                if renewal_tasks_count > 0:
                    RenewalTask.query.filter_by(subscription_id=subscription.id).delete()
                    logger.info(f"删除了 {renewal_tasks_count} 条关联的续费任务记录")

                db.session.delete(subscription)
                logger.info(f"已标记删除订阅 {subscription.id}、{traffic_stats_count} 条流量统计记录和 {renewal_tasks_count} 条续费任务记录")

        except Exception as e:
            logger.error(f"删除订阅 {subscription.id} 和关联数据失败: {e}")
            raise

    def _get_or_create_subscription(self, order: Order):
        """获取或创建订单的订阅记录"""
        try:
            # 使用SQLAlchemy模型查询
            subscription = Subscription.query.filter_by(order_id=order.id).first()

            if subscription:
                return subscription

            # 如果不存在，创建新订阅
            import secrets
            subscription_token = secrets.token_urlsafe(32)

            # 从产品获取分组ID
            group_id = None
            if order.product and order.product.target_group_id:
                group_id = order.product.target_group_id

            # 创建新订阅
            subscription = Subscription(
                order_id=order.id,
                subscription_token=subscription_token,
                is_active=True,
                group_id=group_id,
                expires_at=order.expires_at
            )

            db.session.add(subscription)
            db.session.commit()

            logger.info(f"为订单 {order.id} 创建订阅，分组ID: {group_id}")
            return subscription

        except Exception as e:
            logger.error(f"获取或创建订单 {order.id} 订阅失败: {e}")
            db.session.rollback()
            return None

    def get_user_subscriptions(self, user_id: int) -> List[Dict]:
        """获取用户的所有订阅"""
        try:
            # 获取用户信息
            user = User.query.get(user_id)
            if not user:
                logger.warning(f"用户不存在: {user_id}")
                return []

            # 获取用户的所有已完成订单
            # 优先使用user_id匹配，避免邮箱冲突导致的权限问题
            orders = Order.query.filter(
                Order.user_id == user_id,
                Order.status == OrderStatus.COMPLETED
            ).order_by(Order.created_at.desc()).all()

            # 如果没有找到通过user_id关联的订单，再尝试通过邮箱匹配
            # 但只有当用户没有任何user_id关联的订单时才这样做
            if not orders:
                orders = Order.query.filter(
                    Order.customer_email == user.email,
                    Order.user_id.is_(None),  # 只匹配没有user_id的订单
                    Order.status == OrderStatus.COMPLETED
                ).order_by(Order.created_at.desc()).all()

            result = []
            for order in orders:
                # 获取或创建订阅
                subscription = self._get_or_create_subscription(order)
                if not subscription:
                    continue

                # 获取详细的流量统计（MB单位）
                traffic_stats = self._get_order_traffic_stats(order)

                # 获取订阅信息
                sub_info = {
                    'id': subscription.id,
                    'order_id': order.order_id,  # 使用order_id字符串而不是数据库ID
                    'product_name': order.product.name if order.product else f"{order.node_type.value.upper()} 套餐",
                    'node_type': order.node_type.value,  # 添加节点类型
                    'duration_days': order.duration_days,  # 添加有效期天数
                    'price': order.price,  # 添加价格
                    'node_count': len(order.node_configs),  # 添加节点数量
                    'token': subscription.subscription_token,
                    'created_at': subscription.created_at,
                    'expires_at': subscription.expires_at,
                    'is_active': subscription.is_active and not subscription.is_expired,
                    'is_expired': subscription.is_expired,
                    'subscription_url': self.generate_order_subscription_url(subscription.subscription_token, 'http://localhost:5000'),
                    'traffic_stats': traffic_stats,
                    'configs': [config.to_dict() for config in order.node_configs if config.is_active]
                }
                result.append(sub_info)

            return result
        except Exception as e:
            logger.error(f"获取用户订阅失败: {e}")
            return []

    def _get_manager_for_order(self, order: Order):
        """根据订单获取合适的MultiXUIManager实例"""
        try:
            # 延迟导入避免循环依赖
            from multi_xui_manager import MultiXUIManager
            
            # 如果产品指定了目标分组，使用该分组的面板
            if order.product and order.product.target_group_id:
                logger.debug(f"订单 {order.order_id} 使用产品分组 {order.product.target_group_id}")
                return MultiXUIManager.from_group(order.product.target_group_id)
            else:
                logger.debug(f"订单 {order.order_id} 使用默认面板配置")
                return MultiXUIManager()
        except Exception as e:
            logger.error(f"为订单 {order.order_id} 创建管理器失败: {e}")
            # 延迟导入
            from multi_xui_manager import MultiXUIManager
            return MultiXUIManager()

    # {{CHENGQI:
    # Action: Removed
    # Timestamp: 2025-06-08 17:52:00 +08:00
    # Task_ID: P4-LD-002
    # Principle_Applied: YAGNI - 移除不再使用的API调用方法
    # Language: Python
    # Description: 移除_get_order_traffic_usage方法，因为现在统一使用数据库查询
    # }}
    # _get_order_traffic_usage方法已移除，现在统一使用_get_order_traffic_stats从数据库获取流量数据

    def _get_order_traffic_stats(self, order: Order) -> Dict:
        """获取订单的流量统计 - 从数据库查询而非API调用"""
        try:
            # {{CHENGQI:
            # Action: Modified
            # Timestamp: 2025-06-08 17:50:00 +08:00
            # Task_ID: P4-LD-002
            # Principle_Applied: YAGNI - 移除不必要的API调用，直接使用数据库数据
            # Language: Python
            # Description: 修改流量获取逻辑，从数据库TrafficStats表查询而非实时调用X-UI API
            # }}

            # 使用TrafficStatsService从数据库获取流量统计
            from services.traffic_stats_service import TrafficStatsService
            traffic_service = TrafficStatsService()

            # 从数据库获取流量统计
            traffic_stats = traffic_service.get_order_traffic_stats_from_db(order)

            logger.debug(f"从数据库获取订单 {order.order_id} 流量统计: {traffic_stats['total_traffic_mb']} MB")

            return traffic_stats

        except Exception as e:
            logger.error(f"获取订单流量统计失败 order_id={order.order_id}: {e}")
            traffic_limit_mb = order.traffic_limit_gb * 1024
            return {
                'total_up_mb': 0,
                'total_down_mb': 0,
                'total_traffic_mb': 0,
                'traffic_limit_mb': traffic_limit_mb,
                'usage_percentage': 0,
                'remaining_mb': traffic_limit_mb,
                'last_updated': None
            }
    
    def generate_subscription_url(self, user_id: int, base_url: str) -> str:
        """生成用户的订阅链接"""
        # 生成订阅令牌（使用用户ID的简单编码）
        token = base64.b64encode(f"user_{user_id}".encode()).decode()
        return f"{base_url}/subscription/{token}"
    
    def get_subscription_configs(self, token: str) -> Optional[str]:
        """根据订阅令牌获取Base64编码的配置"""
        try:
            # 解码令牌获取用户ID
            decoded = base64.b64decode(token).decode()
            if not decoded.startswith('user_'):
                return None
            
            user_id = int(decoded.replace('user_', ''))
            
            # 获取用户的有效订阅
            subscriptions = self.get_user_subscriptions(user_id)
            
            # 收集所有有效的配置
            configs = []
            for subscription in subscriptions:
                if subscription['is_active'] and not subscription['is_expired']:
                    for config in subscription['configs']:
                        if config['is_active'] and config['vless_config']:
                            configs.append(config['vless_config'])
            
            if not configs:
                return None
            
            # 将所有配置合并为一个字符串，每行一个配置
            config_text = '\n'.join(configs)
            
            # Base64编码
            encoded_config = base64.b64encode(config_text.encode('utf-8')).decode('utf-8')
            
            return encoded_config
            
        except Exception as e:
            logger.error(f"生成订阅配置失败 token={token}: {e}")
            return None
    
    def get_user_by_email_subscriptions(self, email: str) -> List[Dict]:
        """根据邮箱获取用户订阅（兼容旧的邮箱查询方式）"""
        try:
            # 查询该邮箱的所有已完成订单
            orders = Order.query.filter_by(
                customer_email=email,
                status=OrderStatus.COMPLETED
            ).order_by(Order.created_at.desc()).all()
            
            subscriptions = []
            for order in orders:
                # 检查订单是否过期
                is_expired = order.expires_at and order.expires_at < datetime.utcnow()
                
                # 获取节点配置
                node_configs = order.node_configs
                
                # 获取流量统计
                traffic_stats = self._get_order_traffic_stats(order)
                
                subscription = {
                    'order_id': order.order_id,
                    'customer_name': order.customer_name,
                    'product_name': f"{order.node_type.value.upper()} 套餐",
                    'node_type': order.node_type.value,
                    'duration_days': order.duration_days,
                    'traffic_limit_gb': order.traffic_limit_gb,
                    'price': order.price,
                    'created_at': order.created_at,
                    'expires_at': order.expires_at,
                    'is_expired': is_expired,
                    'is_active': not is_expired and len(node_configs) > 0,
                    'node_count': len(node_configs),
                    'traffic_stats': traffic_stats,
                    'configs': [config.to_dict() for config in node_configs]
                }
                subscriptions.append(subscription)
            
            return subscriptions
            
        except Exception as e:
            logger.error(f"根据邮箱获取订阅失败 email={email}: {e}")
            return []
    
    def generate_email_subscription_url(self, email: str, base_url: str) -> str:
        """根据邮箱生成订阅链接"""
        # 生成邮箱订阅令牌
        token = base64.b64encode(f"email_{email}".encode()).decode()
        return f"{base_url}/subscription/{token}"
    
    def get_email_subscription_configs(self, email: str) -> Optional[str]:
        """根据邮箱获取Base64编码的配置"""
        try:
            # 获取邮箱的有效订阅
            subscriptions = self.get_user_by_email_subscriptions(email)
            
            # 收集所有有效的配置
            configs = []
            for subscription in subscriptions:
                if subscription['is_active'] and not subscription['is_expired']:
                    for config in subscription['configs']:
                        if config['is_active'] and config['vless_config']:
                            configs.append(config['vless_config'])
            
            if not configs:
                return None
            
            # 将所有配置合并为一个字符串，每行一个配置
            config_text = '\n'.join(configs)
            
            # Base64编码
            encoded_config = base64.b64encode(config_text.encode('utf-8')).decode('utf-8')
            
            return encoded_config
            
        except Exception as e:
            logger.error(f"根据邮箱生成订阅配置失败 email={email}: {e}")
            return None

    def generate_order_subscription_url(self, subscription_token: str, base_url: str) -> str:
        """根据订阅令牌生成订阅链接"""
        return f"{base_url}/subscription/order/{subscription_token}"

    def get_order_subscription_configs(self, subscription_token: str) -> Optional[str]:
        """根据订阅令牌获取订单的Base64编码配置"""
        try:
            # 查找订阅记录
            subscription = Subscription.query.filter_by(
                subscription_token=subscription_token,
                is_active=True
            ).first()

            if not subscription:
                logger.warning(f"未找到有效的订阅记录: {subscription_token[:8]}...")
                return None

            # 检查订阅是否过期
            if subscription.is_expired:
                logger.warning(f"订阅已过期: {subscription_token[:8]}...")
                return None

            # 获取关联的订单
            order = subscription.order
            if not order or order.status != OrderStatus.COMPLETED:
                logger.warning(f"订单状态无效: {order.order_id if order else 'None'}")
                return None

            # 收集订单的所有有效节点配置（VLESS配置是URL格式）
            configs = []
            for node_config in order.node_configs:
                if node_config.is_active and node_config.vless_config:
                    configs.append(node_config.vless_config)

            if not configs:
                logger.warning(f"订单 {order.order_id} 没有有效的节点配置")
                return None

            # 将所有配置用换行符连接
            config_text = '\n'.join(configs)

            # 对整个内容进行Base64编码（标准v2ray订阅格式）
            encoded_config = base64.b64encode(config_text.encode('utf-8')).decode('utf-8')

            logger.info(f"为订单 {order.order_id} 生成订阅配置，包含 {len(configs)} 个节点")
            return encoded_config

        except Exception as e:
            logger.error(f"根据订阅令牌生成配置失败 token={subscription_token[:8]}...: {e}")
            return None

    def get_subscription_by_token(self, subscription_token: str) -> Optional[Dict]:
        """根据订阅令牌获取订阅信息"""
        try:
            subscription = Subscription.query.filter_by(
                subscription_token=subscription_token,
                is_active=True
            ).first()

            if not subscription:
                return None

            order = subscription.order
            if not order:
                return None

            return {
                'subscription_id': subscription.id,
                'order_id': order.order_id,
                'customer_email': order.customer_email,
                'customer_name': order.customer_name,
                'node_type': order.node_type.value,
                'duration_days': order.duration_days,
                'traffic_limit_gb': order.traffic_limit_gb,
                'created_at': subscription.created_at,
                'expires_at': subscription.expires_at,
                'is_expired': subscription.is_expired,
                'is_active': subscription.is_active,
                'node_count': len(order.node_configs),
                'configs': [config.to_dict() for config in order.node_configs]
            }

        except Exception as e:
            logger.error(f"获取订阅信息失败 token={subscription_token[:8]}...: {e}")
            return None
