"""
流量数据清理服务
最简洁高效的流量数据优化方案
"""
from datetime import datetime, timedelta
from models import db, TrafficStats
from flask import current_app
import logging

logger = logging.getLogger(__name__)

class TrafficDataCleanupService:
    """流量数据清理服务"""
    
    def __init__(self):
        self.retention_days = None
    
    def get_retention_days(self):
        """获取数据保留天数配置"""
        if self.retention_days is None:
            self.retention_days = current_app.config.get('TRAFFIC_DATA_RETENTION_DAYS', 30)
        return self.retention_days
    
    def cleanup_old_traffic_data(self):
        """
        清理旧的流量数据
        
        Returns:
            dict: 清理结果
        """
        try:
            retention_days = self.get_retention_days()
            cutoff_date = datetime.now() - timedelta(days=retention_days)
            
            logger.info(f"开始清理 {cutoff_date} 之前的流量数据（保留 {retention_days} 天）")
            
            # 查询需要删除的记录数量
            old_records = TrafficStats.query.filter(
                TrafficStats.recorded_at < cutoff_date
            ).all()
            
            old_count = len(old_records)
            
            if old_count == 0:
                logger.info("没有需要清理的旧流量数据")
                return {
                    'success': True,
                    'deleted_count': 0,
                    'message': '没有需要清理的数据',
                    'cutoff_date': cutoff_date.isoformat(),
                    'retention_days': retention_days
                }
            
            # 计算清理的数据量
            total_bytes = 0
            subscription_stats = {}
            
            for record in old_records:
                # 统计流量
                record_bytes = (record.upload_bytes or 0) + (record.download_bytes or 0)
                total_bytes += record_bytes
                
                # 按订阅统计
                sub_id = record.subscription_id
                if sub_id not in subscription_stats:
                    subscription_stats[sub_id] = {'count': 0, 'bytes': 0}
                subscription_stats[sub_id]['count'] += 1
                subscription_stats[sub_id]['bytes'] += record_bytes
            
            # 执行删除
            for record in old_records:
                db.session.delete(record)
            
            db.session.commit()
            
            # 计算节省的空间
            total_mb = total_bytes / (1024 * 1024)
            
            logger.info(f"流量数据清理完成：删除了 {old_count} 条记录，释放约 {total_mb:.2f} MB 数据")
            
            # 记录详细统计
            for sub_id, stats in subscription_stats.items():
                sub_mb = stats['bytes'] / (1024 * 1024)
                logger.info(f"  订阅 {sub_id}: 删除 {stats['count']} 条记录，释放 {sub_mb:.2f} MB")
            
            return {
                'success': True,
                'deleted_count': old_count,
                'total_bytes_freed': total_bytes,
                'total_mb_freed': total_mb,
                'subscription_stats': subscription_stats,
                'message': f'成功清理 {old_count} 条旧记录，释放 {total_mb:.2f} MB 空间',
                'cutoff_date': cutoff_date.isoformat(),
                'retention_days': retention_days
            }
            
        except Exception as e:
            logger.error(f"清理流量数据失败: {e}")
            db.session.rollback()
            return {
                'success': False,
                'error': str(e),
                'message': f'清理失败: {e}'
            }
    
    def get_cleanup_preview(self):
        """
        预览将要清理的数据（不实际删除）
        
        Returns:
            dict: 预览结果
        """
        try:
            retention_days = self.get_retention_days()
            cutoff_date = datetime.now() - timedelta(days=retention_days)
            
            # 查询将要删除的记录
            old_records_query = TrafficStats.query.filter(
                TrafficStats.recorded_at < cutoff_date
            )
            
            old_count = old_records_query.count()
            
            if old_count == 0:
                return {
                    'success': True,
                    'will_delete_count': 0,
                    'message': '没有需要清理的数据',
                    'cutoff_date': cutoff_date.isoformat(),
                    'retention_days': retention_days
                }
            
            # 计算将要清理的数据量
            total_bytes = 0
            subscription_stats = {}
            
            for record in old_records_query.all():
                record_bytes = (record.upload_bytes or 0) + (record.download_bytes or 0)
                total_bytes += record_bytes
                
                sub_id = record.subscription_id
                if sub_id not in subscription_stats:
                    subscription_stats[sub_id] = {'count': 0, 'bytes': 0}
                subscription_stats[sub_id]['count'] += 1
                subscription_stats[sub_id]['bytes'] += record_bytes
            
            total_mb = total_bytes / (1024 * 1024)
            
            return {
                'success': True,
                'will_delete_count': old_count,
                'total_bytes_to_free': total_bytes,
                'total_mb_to_free': total_mb,
                'subscription_stats': subscription_stats,
                'message': f'将清理 {old_count} 条记录，释放约 {total_mb:.2f} MB 空间',
                'cutoff_date': cutoff_date.isoformat(),
                'retention_days': retention_days
            }
            
        except Exception as e:
            logger.error(f"预览清理数据失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': f'预览失败: {e}'
            }
    
    def get_current_stats(self):
        """
        获取当前流量数据统计
        
        Returns:
            dict: 当前统计信息
        """
        try:
            retention_days = self.get_retention_days()
            cutoff_date = datetime.now() - timedelta(days=retention_days)
            
            # 总记录数
            total_count = TrafficStats.query.count()
            
            # 保留期内的记录数
            recent_count = TrafficStats.query.filter(
                TrafficStats.recorded_at >= cutoff_date
            ).count()
            
            # 将要清理的记录数
            old_count = total_count - recent_count
            
            # 最早和最晚记录
            earliest_record = TrafficStats.query.order_by(TrafficStats.recorded_at.asc()).first()
            latest_record = TrafficStats.query.order_by(TrafficStats.recorded_at.desc()).first()
            
            return {
                'success': True,
                'total_records': total_count,
                'recent_records': recent_count,
                'old_records': old_count,
                'retention_days': retention_days,
                'cutoff_date': cutoff_date.isoformat(),
                'earliest_record': earliest_record.recorded_at.isoformat() if earliest_record else None,
                'latest_record': latest_record.recorded_at.isoformat() if latest_record else None,
                'optimization_ratio': f"{(old_count / total_count * 100):.1f}%" if total_count > 0 else "0%"
            }
            
        except Exception as e:
            logger.error(f"获取流量数据统计失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': f'获取统计失败: {e}'
            }

# 创建全局实例
traffic_data_cleanup_service = TrafficDataCleanupService()
