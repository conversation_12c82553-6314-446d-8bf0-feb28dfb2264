<!DOCTYPE html>
<html>
<head>
    <title>流量优化测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>流量优化功能测试</h1>
        
        <div class="row mt-4">
            <div class="col-12">
                <button type="button" class="btn btn-warning" onclick="testPreview()">测试预览</button>
                <button type="button" class="btn btn-danger" onclick="testCleanup()">测试清理</button>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">测试日志</div>
                    <div class="card-body">
                        <div id="test-log"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function addTestLog(message) {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleString();
            logDiv.innerHTML += `<p><small>${timestamp}</small> - ${message}</p>`;
        }
        
        function testPreview() {
            addTestLog('开始测试预览功能...');
            
            fetch('/admin/api/traffic-optimization/preview')
                .then(response => {
                    addTestLog(`预览API响应状态: ${response.status}`);
                    return response.json();
                })
                .then(data => {
                    addTestLog(`预览API响应数据: ${JSON.stringify(data)}`);
                    if (data.success && data.preview) {
                        addTestLog('✅ 预览功能正常');
                    } else {
                        addTestLog('❌ 预览功能异常');
                    }
                })
                .catch(error => {
                    addTestLog(`❌ 预览功能错误: ${error.message}`);
                });
        }
        
        function testCleanup() {
            addTestLog('开始测试清理功能...');
            
            fetch('/admin/api/traffic-optimization/cleanup', {method: 'POST'})
                .then(response => {
                    addTestLog(`清理API响应状态: ${response.status}`);
                    return response.json();
                })
                .then(data => {
                    addTestLog(`清理API响应数据: ${JSON.stringify(data)}`);
                    if (data.success) {
                        addTestLog('✅ 清理功能正常');
                    } else {
                        addTestLog('❌ 清理功能异常');
                    }
                })
                .catch(error => {
                    addTestLog(`❌ 清理功能错误: ${error.message}`);
                });
        }
        
        // 页面加载完成后自动测试
        document.addEventListener('DOMContentLoaded', function() {
            addTestLog('页面加载完成，开始自动测试...');
            setTimeout(testPreview, 1000);
            setTimeout(testCleanup, 2000);
        });
    </script>
</body>
</html>