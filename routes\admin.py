"""
管理员路由
"""
from flask import Blueprint, request, session, render_template, redirect, url_for, flash, jsonify
from models import db, User, XUIPanel, Product, Order, PanelStatus, ProductType, NodeType, UserRole, OrderStatus, XUIPanelGroup, XUIPanelGroupMembership, GroupRole, Subscription, TrafficStats, EmailConfig, VerificationCode, VerificationCodeType, Coupon, RenewalPricing, RenewalTask, ProtocolTemplate, NodeConfig
from functools import wraps
from datetime import datetime
import logging
import time

logger = logging.getLogger(__name__)

admin_bp = Blueprint('admin', __name__)

def admin_required(f):
    """管理员权限装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('请先登录', 'error')
            return redirect(url_for('auth.login'))

        user = User.query.get(session['user_id'])
        if not user or user.role != UserRole.ADMIN:
            flash('需要管理员权限', 'error')
            return redirect(url_for('shop.shop_index'))

        return f(*args, **kwargs)
    return decorated_function

# ==================== 管理员认证 ====================

@admin_bp.route('/login', methods=['GET', 'POST'])
def admin_login():
    """管理员登录"""
    if request.method == 'GET':
        # 检查是否有管理员账户
        admin_exists = User.query.filter_by(role=UserRole.ADMIN).first() is not None
        return render_template('admin/login.html', show_register_link=not admin_exists)

    try:
        # 验证必需字段
        username = request.form.get('username')
        password = request.form.get('password')

        if not username or not password:
            flash('用户名和密码不能为空', 'error')
            return redirect(url_for('admin.admin_login'))

        # 查找管理员用户
        user = User.query.filter(
            (User.username == username) | (User.email == username),
            User.role == UserRole.ADMIN
        ).first()

        if not user or not user.check_password(password):
            flash('管理员用户名或密码错误', 'error')
            return redirect(url_for('admin.admin_login'))

        if not user.is_active:
            flash('管理员账户已被禁用', 'error')
            return redirect(url_for('admin.admin_login'))

        # 更新最后登录时间
        user.last_login = datetime.now()
        db.session.commit()

        # 设置session
        session['user_id'] = user.id
        session['username'] = user.username
        session['role'] = user.role.value

        flash('管理员登录成功', 'success')
        return redirect(url_for('admin.dashboard'))

    except Exception as e:
        logger.error(f"管理员登录失败: {e}")
        flash('登录失败', 'error')
        return redirect(url_for('admin.admin_login'))

@admin_bp.route('/register', methods=['GET', 'POST'])
def admin_register():
    """管理员注册（仅在没有管理员时可用）"""
    # 检查是否已有管理员
    admin_exists = User.query.filter_by(role=UserRole.ADMIN).first()
    if admin_exists:
        flash('系统已有管理员账户', 'error')
        return redirect(url_for('admin.admin_login'))

    if request.method == 'GET':
        return render_template('admin/register.html')

    try:
        # 验证必需字段
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')

        if not all([username, email, password, confirm_password]):
            flash('请填写所有必需字段', 'error')
            return redirect(url_for('admin.admin_register'))

        if password != confirm_password:
            flash('两次输入的密码不一致', 'error')
            return redirect(url_for('admin.admin_register'))

        if len(password) < 6:
            flash('密码长度至少6位', 'error')
            return redirect(url_for('admin.admin_register'))

        # 检查用户名和邮箱是否已存在
        if User.query.filter_by(username=username).first():
            flash('用户名已存在', 'error')
            return redirect(url_for('admin.admin_register'))

        if User.query.filter_by(email=email).first():
            flash('邮箱已存在', 'error')
            return redirect(url_for('admin.admin_register'))

        # 创建管理员用户
        admin_user = User(
            username=username,
            email=email,
            full_name=request.form.get('full_name'),
            role=UserRole.ADMIN  # 设置为管理员角色
        )
        admin_user.set_password(password)

        db.session.add(admin_user)
        db.session.commit()

        # 自动登录
        session['user_id'] = admin_user.id
        session['username'] = admin_user.username
        session['role'] = admin_user.role.value

        flash('管理员账户创建成功，欢迎使用系统！', 'success')
        return redirect(url_for('admin.dashboard'))

    except Exception as e:
        logger.error(f"创建管理员账户失败: {e}")
        db.session.rollback()
        flash('创建管理员账户失败', 'error')
        return redirect(url_for('admin.admin_register'))

# ==================== 管理员仪表板 ====================

@admin_bp.route('/')
@admin_bp.route('/dashboard')
@admin_required
def dashboard():
    """管理员仪表板"""
    try:
        stats = _get_dashboard_stats()
        return render_template('admin/dashboard.html', stats=stats)
    except Exception as e:
        logger.error(f"获取仪表板数据失败: {e}")
        flash('获取仪表板数据失败', 'error')
        return redirect(url_for('shop.shop_index'))

# ==================== 用户管理 ====================

@admin_bp.route('/users')
@admin_required
def users():
    """用户管理页面"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = 20

        users = User.query.paginate(
            page=page, per_page=per_page, error_out=False
        )

        return render_template('admin/users.html', users=users)
    except Exception as e:
        logger.error(f"获取用户列表失败: {e}")
        flash('获取用户列表失败', 'error')
        return redirect(url_for('admin.dashboard'))

@admin_bp.route('/users/create', methods=['GET', 'POST'])
@admin_required
def create_user():
    """创建用户"""
    if request.method == 'GET':
        return render_template('admin/create_user.html')

    try:
        # 验证必需字段
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')

        if not all([username, email, password]):
            flash('请填写所有必需字段', 'error')
            return redirect(url_for('admin.create_user'))

        # 检查用户名和邮箱是否已存在
        if User.query.filter_by(username=username).first():
            flash('用户名已存在', 'error')
            return redirect(url_for('admin.create_user'))

        if User.query.filter_by(email=email).first():
            flash('邮箱已存在', 'error')
            return redirect(url_for('admin.create_user'))

        # 创建用户
        user = User(
            username=username,
            email=email,
            full_name=request.form.get('full_name'),
            phone=request.form.get('phone'),
            role=UserRole(request.form.get('role', 'user'))
        )
        user.set_password(password)

        db.session.add(user)
        db.session.commit()

        flash('用户创建成功', 'success')
        return redirect(url_for('admin.users'))

    except Exception as e:
        logger.error(f"创建用户失败: {e}")
        db.session.rollback()
        flash('创建用户失败', 'error')
        return redirect(url_for('admin.create_user'))

@admin_bp.route('/users/<int:user_id>/delete', methods=['POST'])
@admin_required
def delete_user(user_id):
    """删除用户（仅限普通用户）"""
    try:
        # 获取要删除的用户
        user = User.query.get_or_404(user_id)

        # 检查权限：只能删除普通用户
        if user.role == UserRole.ADMIN:
            flash('不能删除管理员用户', 'error')
            return redirect(url_for('admin.users'))

        # 检查是否是当前登录用户
        if user.id == session['user_id']:
            flash('不能删除当前登录的用户', 'error')
            return redirect(url_for('admin.users'))

        # 记录删除操作
        logger.info(f"管理员 {session['username']} 删除用户 {user.username} (ID: {user.id})")

        # 处理用户关联数据
        deleted_subscriptions = 0
        deleted_orders = 0
        deleted_traffic_stats = 0
        deleted_renewal_tasks = 0
        failed_xui_deletions = []

        # 1. 处理用户的订阅和订单
        user_orders = Order.query.filter_by(user_id=user.id).all()

        for order in user_orders:
            # 获取订单关联的订阅
            subscription = order.subscription

            if subscription:
                # 从X-UI面板删除客户端
                try:
                    from services.subscription_service import subscription_service
                    deleted_clients, failed_deletions = subscription_service.delete_subscription_clients_from_xui(subscription)

                    if failed_deletions:
                        failed_xui_deletions.extend(failed_deletions)
                        logger.warning(f"删除用户 {user.username} 时，X-UI客户端删除失败: {failed_deletions}")

                    if deleted_clients:
                        logger.info(f"成功从X-UI面板删除 {len(deleted_clients)} 个客户端")

                except Exception as e:
                    logger.error(f"删除用户 {user.username} 的X-UI客户端时出错: {e}")
                    failed_xui_deletions.append(f"订阅 {subscription.id}: {str(e)}")

                # 删除关联的流量统计记录
                traffic_count = TrafficStats.query.filter_by(subscription_id=subscription.id).count()
                if traffic_count > 0:
                    TrafficStats.query.filter_by(subscription_id=subscription.id).delete()
                    deleted_traffic_stats += traffic_count

                # 删除关联的续费任务记录
                from models import RenewalTask
                renewal_count = RenewalTask.query.filter_by(subscription_id=subscription.id).count()
                if renewal_count > 0:
                    RenewalTask.query.filter_by(subscription_id=subscription.id).delete()
                    deleted_renewal_tasks += renewal_count

                # 删除订阅
                db.session.delete(subscription)
                deleted_subscriptions += 1

            # 删除订单（会级联删除节点配置）
            db.session.delete(order)
            deleted_orders += 1

        # 2. 删除用户的流量统计记录（如果有直接关联的）
        user_traffic_count = TrafficStats.query.filter_by(user_id=user.id).count()
        if user_traffic_count > 0:
            TrafficStats.query.filter_by(user_id=user.id).delete()
            deleted_traffic_stats += user_traffic_count

        # 3. 删除用户记录
        username = user.username  # 保存用户名用于日志
        db.session.delete(user)

        # 提交所有更改
        db.session.commit()

        # 构建成功消息
        message_parts = [f'用户 "{username}" 删除成功']
        if deleted_orders > 0:
            message_parts.append(f'删除了 {deleted_orders} 个订单')
        if deleted_subscriptions > 0:
            message_parts.append(f'删除了 {deleted_subscriptions} 个订阅')
        if deleted_traffic_stats > 0:
            message_parts.append(f'删除了 {deleted_traffic_stats} 条流量统计记录')
        if deleted_renewal_tasks > 0:
            message_parts.append(f'删除了 {deleted_renewal_tasks} 条续费任务记录')

        success_message = '；'.join(message_parts)

        if failed_xui_deletions:
            success_message += f'；但有 {len(failed_xui_deletions)} 个X-UI客户端删除失败'
            logger.warning(f"X-UI客户端删除失败详情: {failed_xui_deletions}")

        flash(success_message, 'success')
        logger.info(f"用户 {username} 删除完成: {success_message}")

        return redirect(url_for('admin.users'))

    except Exception as e:
        logger.error(f"删除用户失败: {e}")
        db.session.rollback()
        flash('删除用户失败', 'error')
        return redirect(url_for('admin.users'))

# ==================== X-UI面板管理 ====================

@admin_bp.route('/panels')
@admin_required
def panels():
    """X-UI面板管理页面"""
    try:
        panels = XUIPanel.query.all()

        # 实时检查面板状态
        from multi_xui_manager import MultiXUIManager
        manager = MultiXUIManager()

        # 为每个面板添加实时状态信息
        for panel in panels:
            panel_id = f"panel_{panel.id}"
            if panel_id in manager.panel_status:
                # 执行健康检查
                is_healthy = manager.check_panel_health(panel_id)
                status = manager.panel_status[panel_id]

                # 更新面板的实时状态
                panel.is_online = is_healthy
                panel.response_time = status.response_time * 1000  # 转换为毫秒
                panel.last_error = status.last_error
                panel.current_clients = status.client_count

                # 更新数据库中的状态（可选，用于持久化）
                try:
                    db_panel = XUIPanel.query.get(panel.id)
                    db_panel.is_online = is_healthy
                    db_panel.response_time = status.response_time
                    db_panel.last_error = status.last_error
                    db_panel.current_clients = status.client_count
                    db_panel.last_check = datetime.fromtimestamp(status.last_check) if status.last_check else None
                    db.session.commit()
                except Exception as update_error:
                    logger.warning(f"更新面板 {panel.id} 数据库状态失败: {update_error}")
                    db.session.rollback()
            else:
                # 如果管理器中没有这个面板，标记为离线
                panel.is_online = False
                panel.response_time = 0
                panel.last_error = "面板未在管理器中初始化"
                panel.current_clients = 0

        return render_template('admin/panels.html', panels=panels)
    except Exception as e:
        logger.error(f"获取面板列表失败: {e}")
        flash('获取面板列表失败', 'error')
        return redirect(url_for('admin.dashboard'))

@admin_bp.route('/api/panels/check-status')
@admin_required
def check_panels_status():
    """Ajax API: 检查所有面板的实时状态"""
    try:
        from multi_xui_manager import MultiXUIManager
        from xui_client import XUIClient

        panels = XUIPanel.query.all()
        results = []

        for panel in panels:
            panel_result = {
                'id': panel.id,
                'name': panel.name,
                'base_url': panel.base_url,
                'path_prefix': panel.path_prefix,
                'is_online': False,
                'response_time': 0,
                'error_message': None,
                'last_check': time.time()
            }

            try:
                # 创建独立的客户端进行测试
                client = XUIClient(
                    base_url=panel.base_url,
                    username=panel.username,
                    password=panel.password,
                    path_prefix=panel.path_prefix
                )

                # 设置较短的超时时间进行快速检测
                client.session.timeout = 10

                start_time = time.time()

                # 只测试登录功能 - 能登录就算在线
                login_success = client.login()
                if not login_success:
                    raise Exception("登录验证失败")

                response_time = time.time() - start_time

                # 所有测试通过
                panel_result.update({
                    'is_online': True,
                    'response_time': round(response_time * 1000, 2),  # 转换为毫秒
                    'error_message': None
                })

            except Exception as e:
                error_msg = str(e)
                response_time = time.time() - start_time if 'start_time' in locals() else 0

                panel_result.update({
                    'is_online': False,
                    'response_time': round(response_time * 1000, 2),
                    'error_message': error_msg
                })

                logger.warning(f"面板 {panel.name} 健康检查失败: {error_msg}")

            results.append(panel_result)

        return jsonify({
            'success': True,
            'panels': results,
            'timestamp': time.time()
        })

    except Exception as e:
        logger.error(f"检查面板状态API失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@admin_bp.route('/api/panels/<int:panel_id>/check-status')
@admin_required
def check_single_panel_status(panel_id):
    """Ajax API: 检查单个面板的实时状态"""
    try:
        panel = XUIPanel.query.get_or_404(panel_id)

        from xui_client import XUIClient

        result = {
            'id': panel.id,
            'name': panel.name,
            'is_online': False,
            'response_time': 0,
            'error_message': None,
            'last_check': time.time()
        }

        try:
            # 创建客户端进行测试
            client = XUIClient(
                base_url=panel.base_url,
                username=panel.username,
                password=panel.password,
                path_prefix=panel.path_prefix
            )

            client.session.timeout = 10
            start_time = time.time()

            # 只测试登录功能 - 能登录就算在线
            if not client.login():
                raise Exception("登录失败")

            response_time = time.time() - start_time

            result.update({
                'is_online': True,
                'response_time': round(response_time * 1000, 2),
                'error_message': None
            })

        except Exception as e:
            response_time = time.time() - start_time if 'start_time' in locals() else 0
            result.update({
                'is_online': False,
                'response_time': round(response_time * 1000, 2),
                'error_message': str(e)
            })

        return jsonify({
            'success': True,
            'panel': result
        })

    except Exception as e:
        logger.error(f"检查单个面板状态失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@admin_bp.route('/panels/create', methods=['GET', 'POST'])
@admin_required
def create_panel():
    """添加X-UI面板"""
    if request.method == 'GET':
        return render_template('admin/create_panel.html')

    try:
        # 验证必需字段
        name = request.form.get('name')
        base_url = request.form.get('base_url')
        username = request.form.get('username')
        password = request.form.get('password')

        if not all([name, base_url, username, password]):
            flash('请填写所有必需字段', 'error')
            return redirect(url_for('admin.create_panel'))

        # 创建面板
        panel = XUIPanel(
            name=name,
            base_url=base_url,
            path_prefix=request.form.get('path_prefix', '/'),
            username=username,
            password=password,
            region=request.form.get('region', 'default'),
            max_clients=int(request.form.get('max_clients', 1000)),
            priority=int(request.form.get('priority', 1)),
            status=PanelStatus(request.form.get('status', 'active'))
        )

        db.session.add(panel)
        db.session.commit()

        flash('X-UI面板添加成功', 'success')
        return redirect(url_for('admin.panels'))

    except Exception as e:
        logger.error(f"添加X-UI面板失败: {e}")
        db.session.rollback()
        flash('添加X-UI面板失败', 'error')
        return redirect(url_for('admin.create_panel'))

@admin_bp.route('/panels/<int:panel_id>/delete', methods=['POST'])
@admin_required
def delete_panel(panel_id):
    """删除X-UI面板"""
    try:
        panel = XUIPanel.query.get_or_404(panel_id)
        panel_name = panel.name

        # 在删除面板前，自动清理相关数据
        logger.info(f"开始清理面板 {panel_name} 删除前的相关数据")

        # 获取面板服务器地址
        server_address = panel.base_url.replace('http://', '').replace('https://', '').split(':')[0]

        # 1. 清理该面板相关的节点配置
        orphaned_configs = NodeConfig.query.filter(
            NodeConfig.server_address == server_address,
            NodeConfig.is_active == True
        ).all()

        cleaned_configs = 0
        for config in orphaned_configs:
            config.is_active = False
            cleaned_configs += 1
            logger.info(f"停用节点配置: {config.id} (服务器: {config.server_address})")

        # 2. 清理分组成员关系
        orphaned_memberships = XUIPanelGroupMembership.query.filter_by(panel_id=panel_id).all()
        cleaned_memberships = 0
        for membership in orphaned_memberships:
            db.session.delete(membership)
            cleaned_memberships += 1
            logger.info(f"删除分组成员关系: 面板 {panel_id} -> 分组 {membership.group_id}")

        # 3. 处理流量基准数据（面板删除时的累加流量处理）
        cleaned_traffic_baseline = 0
        try:
            from services.traffic_baseline_deletion_service import traffic_baseline_deletion_service

            # 获取该面板相关的所有订阅
            affected_subscriptions = []
            for config in orphaned_configs:
                if config.order and config.order.subscription:
                    subscription = config.order.subscription
                    if subscription not in affected_subscriptions:
                        affected_subscriptions.append(subscription)

            # 为每个受影响的订阅处理流量基准
            for subscription in affected_subscriptions:
                try:
                    baseline_result = traffic_baseline_deletion_service.handle_panel_deletion_for_subscription(
                        subscription.id, panel_id
                    )
                    if baseline_result.get('success'):
                        cleaned_traffic_baseline += 1
                        logger.info(f"处理订阅 {subscription.id} 的流量基准数据")
                except Exception as e:
                    logger.warning(f"处理订阅 {subscription.id} 流量基准失败: {e}")

        except Exception as e:
            logger.warning(f"处理流量基准数据失败: {e}")

        # 4. 删除面板
        db.session.delete(panel)
        db.session.commit()

        logger.info(f"面板 {panel_name} 删除完成，清理了 {cleaned_configs} 个节点配置、{cleaned_memberships} 个分组关系和 {cleaned_traffic_baseline} 个流量基准")

        message = f'X-UI面板 "{panel_name}" 删除成功'
        cleanup_details = []
        if cleaned_configs > 0:
            cleanup_details.append(f'{cleaned_configs} 个节点配置')
        if cleaned_memberships > 0:
            cleanup_details.append(f'{cleaned_memberships} 个分组关系')
        if cleaned_traffic_baseline > 0:
            cleanup_details.append(f'{cleaned_traffic_baseline} 个流量基准')

        if cleanup_details:
            message += f'，已清理 {", ".join(cleanup_details)}'

        flash(message, 'success')
        return redirect(url_for('admin.panels'))

    except Exception as e:
        logger.error(f"删除X-UI面板失败: {e}")
        db.session.rollback()
        flash('删除X-UI面板失败', 'error')
        return redirect(url_for('admin.panels'))

# ==================== 产品管理 ====================

@admin_bp.route('/products')
@admin_required
def products():
    """产品管理页面"""
    try:
        products = Product.query.all()
        return render_template('admin/products.html', products=products)
    except Exception as e:
        logger.error(f"获取产品列表失败: {e}")
        flash('获取产品列表失败', 'error')
        return redirect(url_for('admin.dashboard'))

@admin_bp.route('/products/create', methods=['GET', 'POST'])
@admin_required
def create_product():
    """创建产品"""
    if request.method == 'GET':
        groups = XUIPanelGroup.query.filter_by(is_active=True).order_by(XUIPanelGroup.priority.asc()).all()
        return render_template('admin/create_product.html', groups=groups)

    try:
        # 验证必需字段
        name = request.form.get('name')
        product_type = request.form.get('product_type')
        duration_days = request.form.get('duration_days')
        traffic_limit_gb = request.form.get('traffic_limit_gb')
        price = request.form.get('price')

        if not all([name, product_type, duration_days, traffic_limit_gb, price]):
            flash('请填写所有必需字段', 'error')
            return redirect(url_for('admin.create_product'))

        # 创建产品
        target_group_id = request.form.get('target_group_id')
        product = Product(
            name=name,
            description=request.form.get('description'),
            product_type=ProductType(product_type),
            duration_days=int(duration_days),
            traffic_limit_gb=int(traffic_limit_gb),
            price=float(price),
            node_type=NodeType(request.form.get('node_type', 'vless')),
            target_group_id=int(target_group_id) if target_group_id else None,
            group_specific_config=request.form.get('group_specific_config'),
            is_active=request.form.get('is_active') == 'on',
            stock_count=int(request.form.get('stock_count', -1))
        )

        db.session.add(product)
        db.session.commit()

        flash('产品创建成功', 'success')
        return redirect(url_for('admin.products'))

    except Exception as e:
        logger.error(f"创建产品失败: {e}")
        db.session.rollback()
        flash('创建产品失败', 'error')
        return redirect(url_for('admin.create_product'))

@admin_bp.route('/products/<int:product_id>/edit', methods=['GET', 'POST'])
@admin_required
def edit_product(product_id):
    """编辑产品"""
    try:
        product = Product.query.get_or_404(product_id)

        if request.method == 'GET':
            # 返回JSON格式的产品数据（用于AJAX请求）
            if request.headers.get('Content-Type') == 'application/json' or request.args.get('format') == 'json':
                groups = XUIPanelGroup.query.filter_by(is_active=True).order_by(XUIPanelGroup.priority.asc()).all()
                return jsonify({
                    'success': True,
                    'product': product.to_dict(),
                    'groups': [group.to_dict() for group in groups]
                })
            else:
                # 如果不是AJAX请求，重定向到产品列表
                return redirect(url_for('admin.products'))

        # POST请求 - 更新产品
        # 获取数据（支持JSON和表单数据）
        if request.is_json:
            data = request.get_json()
            logger.info(f"编辑产品 {product_id} - 接收到JSON数据: {data}")
        else:
            data = request.form
            logger.info(f"编辑产品 {product_id} - 接收到表单数据: {dict(data)}")

        # 验证必需字段
        name = data.get('name')
        product_type = data.get('product_type')
        duration_days = data.get('duration_days')
        traffic_limit_gb = data.get('traffic_limit_gb')
        price = data.get('price')

        logger.info(f"编辑产品 {product_id} - 解析的字段: name={name}, type={product_type}, days={duration_days}, traffic={traffic_limit_gb}, price={price}")

        if not all([name, product_type, duration_days, traffic_limit_gb, price]):
            if request.is_json:
                return jsonify({'success': False, 'message': '请填写所有必需字段'})
            flash('请填写所有必需字段', 'error')
            return redirect(url_for('admin.products'))

        # 检查产品名称是否已存在（排除当前产品）
        existing_product = Product.query.filter(
            Product.name == name,
            Product.id != product_id
        ).first()
        if existing_product:
            if request.is_json:
                return jsonify({'success': False, 'message': '产品名称已存在'})
            flash('产品名称已存在', 'error')
            return redirect(url_for('admin.products'))

        # 更新产品信息
        product.name = name
        product.description = data.get('description')
        product.product_type = ProductType(product_type)
        product.duration_days = int(duration_days)
        product.traffic_limit_gb = int(traffic_limit_gb)
        product.price = float(price)
        product.node_type = NodeType(data.get('node_type', 'vless'))

        target_group_id = data.get('target_group_id')
        if target_group_id and target_group_id != '' and target_group_id != 'null':
            product.target_group_id = int(target_group_id)
        else:
            product.target_group_id = None

        group_config = data.get('group_specific_config')
        if group_config and group_config.strip() != '':
            product.group_specific_config = group_config.strip()
        else:
            product.group_specific_config = None
        # 处理复选框值（JSON请求中为true/false，表单请求中为'on'/None）
        is_active = data.get('is_active')
        if isinstance(is_active, bool):
            product.is_active = is_active
        else:
            product.is_active = is_active == 'on'
        product.stock_count = int(data.get('stock_count', -1))

        # 手动更新时间戳
        product.updated_at = datetime.now()

        logger.info(f"编辑产品 {product_id} - 准备提交数据库更改")
        db.session.commit()
        logger.info(f"编辑产品 {product_id} - 数据库更改已提交")

        if request.is_json:
            return jsonify({'success': True, 'message': '产品更新成功'})
        flash('产品更新成功', 'success')
        return redirect(url_for('admin.products'))

    except Exception as e:
        logger.error(f"编辑产品失败: {e}")
        db.session.rollback()
        if request.is_json:
            return jsonify({'success': False, 'message': '编辑产品失败'})
        flash('编辑产品失败', 'error')
        return redirect(url_for('admin.products'))

# ==================== 分组管理 ====================

@admin_bp.route('/groups')
@admin_required
def groups():
    """分组管理页面"""
    try:
        groups = XUIPanelGroup.query.order_by(XUIPanelGroup.priority.asc()).all()
        return render_template('admin/groups.html', groups=groups)
    except Exception as e:
        logger.error(f"获取分组列表失败: {e}")
        flash('获取分组列表失败', 'error')
        return redirect(url_for('admin.dashboard'))

@admin_bp.route('/groups/create', methods=['GET', 'POST'])
@admin_required
def create_group():
    """创建分组"""
    if request.method == 'GET':
        return render_template('admin/create_group.html')

    try:
        # 验证必需字段
        name = request.form.get('name')
        if not name:
            flash('分组名称不能为空', 'error')
            return redirect(url_for('admin.create_group'))

        # 检查分组名称是否已存在
        if XUIPanelGroup.query.filter_by(name=name).first():
            flash('分组名称已存在', 'error')
            return redirect(url_for('admin.create_group'))

        # 创建分组
        group = XUIPanelGroup(
            name=name,
            description=request.form.get('description'),
            color=request.form.get('color', '#007bff'),
            priority=int(request.form.get('priority', 1)),
            is_active=request.form.get('is_active') == 'on'
        )

        db.session.add(group)
        db.session.commit()

        flash('分组创建成功', 'success')
        return redirect(url_for('admin.groups'))

    except Exception as e:
        logger.error(f"创建分组失败: {e}")
        db.session.rollback()
        flash('创建分组失败', 'error')
        return redirect(url_for('admin.create_group'))

@admin_bp.route('/groups/<int:group_id>/edit', methods=['GET', 'POST'])
@admin_required
def edit_group(group_id):
    """编辑分组"""
    try:
        group = XUIPanelGroup.query.get_or_404(group_id)

        if request.method == 'GET':
            return render_template('admin/edit_group.html', group=group)

        # 验证必需字段
        name = request.form.get('name')
        if not name:
            flash('分组名称不能为空', 'error')
            return redirect(url_for('admin.edit_group', group_id=group_id))

        # 检查分组名称是否已存在（排除当前分组）
        existing_group = XUIPanelGroup.query.filter(
            XUIPanelGroup.name == name,
            XUIPanelGroup.id != group_id
        ).first()
        if existing_group:
            flash('分组名称已存在', 'error')
            return redirect(url_for('admin.edit_group', group_id=group_id))

        # 更新分组
        group.name = name
        group.description = request.form.get('description')
        group.color = request.form.get('color', '#007bff')
        group.priority = int(request.form.get('priority', 1))
        group.is_active = request.form.get('is_active') == 'on'

        db.session.commit()

        flash('分组更新成功', 'success')
        return redirect(url_for('admin.groups'))

    except Exception as e:
        logger.error(f"编辑分组失败: {e}")
        db.session.rollback()
        flash('编辑分组失败', 'error')
        return redirect(url_for('admin.groups'))

@admin_bp.route('/groups/<int:group_id>/delete', methods=['POST'])
@admin_required
def delete_group(group_id):
    """删除分组"""
    try:
        group = XUIPanelGroup.query.get_or_404(group_id)

        # 检查是否有产品关联到这个分组
        if group.products:
            flash(f'无法删除分组 "{group.name}"，还有产品关联到此分组', 'error')
            return redirect(url_for('admin.groups'))

        db.session.delete(group)
        db.session.commit()

        flash('分组删除成功', 'success')
        return redirect(url_for('admin.groups'))

    except Exception as e:
        logger.error(f"删除分组失败: {e}")
        db.session.rollback()
        flash('删除分组失败', 'error')
        return redirect(url_for('admin.groups'))

@admin_bp.route('/api/groups/<int:group_id>/batch-sync', methods=['POST'])
@admin_required
def batch_sync_group(group_id):
    """批量同步分组订阅API"""
    try:
        from services.subscription_sync_service import SubscriptionSyncService

        # 检查分组是否存在
        group = XUIPanelGroup.query.get_or_404(group_id)

        logger.info(f"开始批量同步分组 {group.name} (ID: {group_id})")

        # 执行批量同步
        sync_service = SubscriptionSyncService()
        result = sync_service.sync_group_all_panels(group_id)

        if result['success']:
            logger.info(f"分组 {group.name} 批量同步完成: 成功 {result['success_count']}, 失败 {result['failed_count']}")
            return jsonify({
                'success': True,
                'message': '批量同步完成',
                'group_name': result.get('group_name', group.name),
                'affected_subscriptions': result.get('affected_subscriptions', 0),
                'panel_count': result.get('panel_count', 0),
                'success_count': result.get('success_count', 0),
                'failed_count': result.get('failed_count', 0),
                'panel_results': result.get('panel_results', [])
            })
        else:
            error_msg = result.get('error', '批量同步失败')
            logger.error(f"分组 {group.name} 批量同步失败: {error_msg}")
            return jsonify({
                'success': False,
                'error': error_msg
            }), 500

    except Exception as e:
        logger.error(f"批量同步分组 {group_id} 时发生错误: {e}")
        return jsonify({
            'success': False,
            'error': f'批量同步过程中发生错误: {str(e)}'
        }), 500

@admin_bp.route('/groups/<int:group_id>/panels')
@admin_required
def group_panels(group_id):
    """分组面板管理页面"""
    try:
        group = XUIPanelGroup.query.get_or_404(group_id)

        # 获取所有面板
        all_panels = XUIPanel.query.order_by(XUIPanel.name.asc()).all()

        # 获取已在分组中的面板ID列表
        group_panel_ids = [membership.panel_id for membership in group.memberships]

        # 获取所有活跃的协议模板
        protocol_templates = ProtocolTemplate.query.filter_by(is_active=True).order_by(
            ProtocolTemplate.protocol_type, ProtocolTemplate.name
        ).all()

        return render_template('admin/group_panels.html',
                             group=group,
                             all_panels=all_panels,
                             group_panel_ids=group_panel_ids,
                             protocol_templates=protocol_templates)
    except Exception as e:
        logger.error(f"获取分组面板页面失败: {e}")
        flash('获取分组面板页面失败', 'error')
        return redirect(url_for('admin.groups'))

@admin_bp.route('/groups/<int:group_id>/panels/api')
@admin_required
def api_group_panels(group_id):
    """API: 获取分组中的面板列表"""
    try:
        group = XUIPanelGroup.query.get_or_404(group_id)

        # 获取分组中的面板
        panels = []
        for membership in group.memberships:
            panel = membership.panel
            if panel:
                panel_data = panel.to_dict()
                panel_data['membership'] = membership.to_dict()
                panels.append(panel_data)

        return jsonify({
            'success': True,
            'group': group.to_dict(),
            'panels': panels
        })
    except Exception as e:
        logger.error(f"获取分组面板API失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@admin_bp.route('/groups/<int:group_id>/panels/add', methods=['POST'])
@admin_required
def add_panel_to_group(group_id):
    """将面板添加到分组"""
    try:
        group = XUIPanelGroup.query.get_or_404(group_id)
        panel_id = request.form.get('panel_id')
        weight = int(request.form.get('weight', 1))
        role = request.form.get('role', 'primary')
        inbound_id = request.form.get('inbound_id')
        protocol_template_id = request.form.get('protocol_template_id')

        if not panel_id:
            flash('请选择要添加的面板', 'error')
            return redirect(url_for('admin.group_panels', group_id=group_id))

        panel = XUIPanel.query.get_or_404(panel_id)

        # 检查面板是否已在分组中
        existing_membership = XUIPanelGroupMembership.query.filter_by(
            panel_id=panel_id, group_id=group_id
        ).first()

        if existing_membership:
            flash(f'面板 "{panel.name}" 已在分组 "{group.name}" 中', 'warning')
            return redirect(url_for('admin.group_panels', group_id=group_id))

        # 创建成员关系
        membership = XUIPanelGroupMembership(
            panel_id=panel_id,
            group_id=group_id,
            weight=weight,
            role=GroupRole(role),
            inbound_id=int(inbound_id) if inbound_id else None,
            protocol_template_id=int(protocol_template_id) if protocol_template_id else None
        )

        db.session.add(membership)
        db.session.commit()

        inbound_info = f" (入站协议ID: {inbound_id})" if inbound_id else ""
        flash(f'面板 "{panel.name}"{inbound_info} 已添加到分组 "{group.name}"', 'success')
        return redirect(url_for('admin.group_panels', group_id=group_id))

    except Exception as e:
        logger.error(f"添加面板到分组失败: {e}")
        db.session.rollback()
        flash('添加面板到分组失败', 'error')
        return redirect(url_for('admin.group_panels', group_id=group_id))

@admin_bp.route('/groups/<int:group_id>/panels/<int:panel_id>/remove', methods=['POST'])
@admin_required
def remove_panel_from_group(group_id, panel_id):
    """从分组中移除面板"""
    try:
        membership = XUIPanelGroupMembership.query.filter_by(
            panel_id=panel_id, group_id=group_id
        ).first_or_404()

        panel_name = membership.panel.name
        group_name = membership.group.name
        panel = membership.panel

        # 在删除分组关系前，先清理该面板中的相关客户端
        deleted_clients = []
        failed_deletions = []

        try:
            # {{CHENGQI:
            # Action: Added
            # Timestamp: 2025-01-27 14:30:00 +08:00
            # Task_ID: P4-LD-003
            # Principle_Applied: SOLID - 单一职责，在删除前保存流量基准
            # Language: Python
            # Description: 在删除面板前更新流量基准，防止历史流量丢失
            # }}

            # 在删除面板前，先更新流量基准
            from services.panel_traffic_baseline_service import PanelTrafficBaselineService
            baseline_service = PanelTrafficBaselineService()
            baseline_result = baseline_service.update_baselines_before_panel_removal(panel_id, group_id)

            if baseline_result['success']:
                logger.info(f"面板 {panel_name} 流量基准更新成功: {baseline_result.get('message', '')}")
            else:
                logger.warning(f"面板 {panel_name} 流量基准更新失败: {baseline_result.get('error', '')}")

            # 获取该分组的所有活跃订阅
            from services.subscription_sync_service import SubscriptionSyncService
            sync_service = SubscriptionSyncService()
            active_subscriptions = sync_service._get_group_active_subscriptions(group_id)

            if active_subscriptions:
                logger.info(f"开始清理面板 {panel_name} 中的 {len(active_subscriptions)} 个订阅的客户端")

                # 创建XUI客户端
                from xui_client import XUIClient
                xui_client = XUIClient(
                    base_url=panel.base_url,
                    username=panel.username,
                    password=panel.password,
                    path_prefix=panel.path_prefix
                )

                # 删除每个订阅在该面板中的客户端
                for subscription in active_subscriptions:
                    if subscription.order and subscription.order.node_configs:
                        for node_config in subscription.order.node_configs:
                            if (node_config.is_active and node_config.client_email and
                                node_config.server_address == panel.base_url.replace('http://', '').replace('https://', '').split(':')[0]):

                                try:
                                    # 尝试从X-UI面板删除客户端
                                    success, error_msg = xui_client.delete_client_by_email(node_config.client_email)
                                    if success:
                                        deleted_clients.append(f"{node_config.client_email}")
                                        logger.info(f"成功从面板 {panel_name} 删除客户端: {node_config.client_email}")

                                        # 删除数据库中的节点配置
                                        db.session.delete(node_config)
                                    else:
                                        failed_deletions.append(f"{node_config.client_email}: {error_msg}")
                                        logger.warning(f"从面板 {panel_name} 删除客户端失败: {node_config.client_email} - {error_msg}")

                                except Exception as e:
                                    failed_deletions.append(f"{node_config.client_email}: {str(e)}")
                                    logger.error(f"删除客户端 {node_config.client_email} 时发生错误: {e}")

        except Exception as e:
            logger.error(f"清理面板客户端时发生错误: {e}")
            failed_deletions.append(f"清理过程异常: {str(e)}")

        # 删除分组关系
        db.session.delete(membership)
        db.session.commit()

        # 自动清理孤儿数据
        logger.info(f"开始清理面板 {panel_name} 从分组 {group_name} 移除后的孤儿数据")

        # 1. 清理该面板相关的孤儿节点配置
        server_address = panel.base_url.replace('http://', '').replace('https://', '').split(':')[0]
        orphaned_configs = NodeConfig.query.filter(
            NodeConfig.server_address == server_address,
            NodeConfig.is_active == True
        ).all()

        cleaned_configs = 0
        affected_subscriptions = []

        for config in orphaned_configs:
            # 检查该节点配置是否还有其他活跃的面板支持
            other_panels = XUIPanel.query.filter(
                XUIPanel.id != panel_id,
                XUIPanel.status == PanelStatus.ACTIVE
            ).all()

            has_support = False
            for other_panel in other_panels:
                other_address = other_panel.base_url.replace('http://', '').replace('https://', '').split(':')[0]
                if other_address == server_address:
                    has_support = True
                    break

            # 如果没有其他面板支持，则停用该节点配置
            if not has_support:
                config.is_active = False
                cleaned_configs += 1
                logger.info(f"停用孤儿节点配置: {config.id} (服务器: {config.server_address})")

                # 收集受影响的订阅
                if config.order and config.order.subscription:
                    subscription = config.order.subscription
                    if subscription not in affected_subscriptions:
                        affected_subscriptions.append(subscription)

        # 2. 处理流量基准数据（面板从分组移除时的累加流量处理）
        cleaned_traffic_baseline = 0
        if affected_subscriptions:
            try:
                from services.traffic_baseline_deletion_service import traffic_baseline_deletion_service

                # 为每个受影响的订阅处理流量基准
                for subscription in affected_subscriptions:
                    try:
                        baseline_result = traffic_baseline_deletion_service.handle_panel_deletion_for_subscription(
                            subscription.id, panel_id
                        )
                        if baseline_result.get('success'):
                            cleaned_traffic_baseline += 1
                            logger.info(f"处理订阅 {subscription.id} 的流量基准数据")
                    except Exception as e:
                        logger.warning(f"处理订阅 {subscription.id} 流量基准失败: {e}")

            except Exception as e:
                logger.warning(f"处理流量基准数据失败: {e}")

        if cleaned_configs > 0 or cleaned_traffic_baseline > 0:
            db.session.commit()
            logger.info(f"自动清理了 {cleaned_configs} 个孤儿节点配置和 {cleaned_traffic_baseline} 个流量基准")

        # 生成反馈消息
        message = f'面板 "{panel_name}" 已从分组 "{group_name}" 中移除'
        cleanup_details = []

        if deleted_clients:
            cleanup_details.append(f'{len(deleted_clients)} 个客户端')
        if cleaned_configs > 0:
            cleanup_details.append(f'{cleaned_configs} 个孤儿节点配置')
        if cleaned_traffic_baseline > 0:
            cleanup_details.append(f'{cleaned_traffic_baseline} 个流量基准')

        if cleanup_details:
            message += f'，已清理 {", ".join(cleanup_details)}'

        if failed_deletions:
            message += f'，{len(failed_deletions)} 个客户端清理失败'
            logger.warning(f"客户端清理失败详情: {failed_deletions}")

        flash(message, 'success' if not failed_deletions else 'warning')
        return redirect(url_for('admin.group_panels', group_id=group_id))

    except Exception as e:
        logger.error(f"从分组移除面板失败: {e}")
        db.session.rollback()
        flash('从分组移除面板失败', 'error')
        return redirect(url_for('admin.group_panels', group_id=group_id))

@admin_bp.route('/api/panels/<int:panel_id>/inbounds')
@admin_required
def get_panel_inbounds(panel_id):
    """获取指定面板的入站协议列表"""
    try:
        panel = XUIPanel.query.get_or_404(panel_id)

        # 创建XUI客户端
        from xui_client import XUIClient
        client = XUIClient(
            base_url=panel.base_url,
            path_prefix=panel.path_prefix,
            username=panel.username,
            password=panel.password
        )

        # 获取入站协议列表
        inbounds = client.get_inbounds()

        if inbounds is None:
            return jsonify({
                'success': False,
                'message': f'无法连接到面板 {panel.name} 或获取入站协议失败'
            }), 500

        # 格式化入站协议数据
        formatted_inbounds = []
        for inbound in inbounds:
            formatted_inbounds.append({
                'id': inbound.get('id'),
                'remark': inbound.get('remark', f"入站协议 {inbound.get('id')}"),
                'protocol': inbound.get('protocol', 'unknown'),
                'port': inbound.get('port'),
                'enable': inbound.get('enable', False)
            })

        return jsonify({
            'success': True,
            'data': formatted_inbounds
        })

    except Exception as e:
        logger.error(f"获取面板入站协议失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取入站协议失败: {str(e)}'
        }), 500

@admin_bp.route('/groups/<int:group_id>/panels/<int:panel_id>/update', methods=['POST'])
@admin_required
def update_panel_in_group(group_id, panel_id):
    """更新面板在分组中的设置"""
    try:
        membership = XUIPanelGroupMembership.query.filter_by(
            panel_id=panel_id, group_id=group_id
        ).first_or_404()

        weight = int(request.form.get('weight', 1))
        role = request.form.get('role', 'primary')
        inbound_id = request.form.get('inbound_id')

        membership.weight = weight
        membership.role = GroupRole(role)
        membership.inbound_id = int(inbound_id) if inbound_id else None

        db.session.commit()

        flash('面板设置更新成功', 'success')
        return redirect(url_for('admin.group_panels', group_id=group_id))

    except Exception as e:
        logger.error(f"更新面板设置失败: {e}")
        db.session.rollback()
        flash('更新面板设置失败', 'error')
        return redirect(url_for('admin.group_panels', group_id=group_id))

# 注意：原来的单个同步路由 sync_group_subscriptions 已被移除
# 现在统一使用批量同步API: /api/groups/<int:group_id>/batch-sync

# 注意：原来的同步状态API get_group_sync_status 已被移除
# 现在使用批量同步API，不再需要单独的状态查询

# ==================== 订单管理 ====================

@admin_bp.route('/orders')
@admin_required
def orders():
    """订单管理页面"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = 20
        status = request.args.get('status')
        customer_email = request.args.get('customer_email')
        order_id = request.args.get('order_id')

        query = Order.query

        # 状态筛选
        if status:
            query = query.filter_by(status=status)

        # 客户邮箱筛选
        if customer_email:
            query = query.filter(Order.customer_email.ilike(f'%{customer_email}%'))

        # 订单号筛选
        if order_id:
            query = query.filter(Order.order_id.ilike(f'%{order_id}%'))

        orders = query.order_by(Order.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        return render_template('admin/orders.html',
                             orders=orders,
                             current_status=status,
                             current_customer_email=customer_email,
                             current_order_id=order_id)
    except Exception as e:
        logger.error(f"获取订单列表失败: {e}")
        flash('获取订单列表失败', 'error')
        return redirect(url_for('admin.dashboard'))

# ==================== 优惠券管理 (Coupon Management) ====================

def _get_dashboard_stats():
    """获取仪表板统计数据的辅助函数"""
    # 统计数据
    total_users = User.query.count()
    total_orders = Order.query.count()
    total_panels = XUIPanel.query.count()
    total_products = Product.query.count()

    # 在线面板数 - 实时检查
    from multi_xui_manager import MultiXUIManager
    try:
        manager = MultiXUIManager()
        health_results = manager.check_all_panels_health()
        online_panels = sum(1 for is_healthy in health_results.values() if is_healthy)
    except Exception as e:
        logger.warning(f"实时检查面板状态失败，使用数据库状态: {e}")
        online_panels = XUIPanel.query.filter_by(is_online=True).count()

    # 今日订单
    from datetime import datetime, timedelta
    today = datetime.now().date()
    today_orders = Order.query.filter(
        Order.created_at >= today,
        Order.created_at < today + timedelta(days=1)
    ).count()

    # 收入统计
    total_revenue = db.session.query(db.func.sum(Order.price)).filter(
        Order.status == OrderStatus.COMPLETED
    ).scalar() or 0

    return {
        'total_users': total_users,
        'total_orders': total_orders,
        'total_panels': total_panels,
        'total_products': total_products,
        'online_panels': online_panels,
        'today_orders': today_orders,
        'total_revenue': total_revenue
    }

@admin_bp.route('/coupons')
@admin_required
def list_coupons():
    """优惠券列表页面"""
    try:
        coupons = Coupon.query.order_by(Coupon.created_at.desc()).all()
        stats = _get_dashboard_stats()
        return render_template('admin/coupons.html', coupons=coupons, stats=stats)
    except Exception as e:
        logger.error(f"获取优惠券列表失败: {e}")
        flash('获取优惠券列表失败', 'error')
        return redirect(url_for('admin.dashboard'))

@admin_bp.route('/coupons/new', methods=['GET', 'POST'])
@admin_required
def create_coupon():
    """创建优惠券"""
    if request.method == 'POST':
        try:
            code = request.form.get('code')
            discount_percentage = request.form.get('discount_percentage')
            max_uses = request.form.get('max_uses')

            if not code or not discount_percentage:
                flash('优惠券代码和折扣百分比不能为空', 'error')
                return redirect(url_for('admin.create_coupon'))

            if Coupon.query.filter_by(code=code).first():
                flash('优惠券代码已存在', 'error')
                return redirect(url_for('admin.create_coupon'))

            try:
                discount_val = float(discount_percentage)
                if not (0 < discount_val <= 100):
                    raise ValueError("Discount must be between 0 and 100")
            except ValueError:
                flash('无效的折扣百分比，请输入0到100之间的数字', 'error')
                return redirect(url_for('admin.create_coupon'))

            # 处理最大使用次数
            max_uses_val = None
            if max_uses and max_uses.strip():
                try:
                    max_uses_val = int(max_uses)
                    if max_uses_val <= 0:
                        raise ValueError("Max uses must be positive")
                except ValueError:
                    flash('无效的最大使用次数，请输入正整数或留空', 'error')
                    return redirect(url_for('admin.create_coupon'))

            coupon = Coupon(
                code=code,
                discount_percentage=discount_val,
                max_uses=max_uses_val,
                is_active=request.form.get('is_active') == 'on'
            )
            db.session.add(coupon)
            db.session.commit()
            flash('优惠券创建成功', 'success')
            return redirect(url_for('admin.list_coupons'))
        except Exception as e:
            db.session.rollback()
            logger.error(f"创建优惠券失败: {e}")
            flash(f'创建优惠券失败: {e}', 'error')
            return redirect(url_for('admin.create_coupon'))

    stats = _get_dashboard_stats()
    return render_template('admin/create_coupon.html', stats=stats)

@admin_bp.route('/coupons/<int:coupon_id>/edit', methods=['GET', 'POST'])
@admin_required
def edit_coupon(coupon_id):
    """编辑优惠券"""
    coupon = Coupon.query.get_or_404(coupon_id)
    if request.method == 'POST':
        try:
            code = request.form.get('code')
            discount_percentage = request.form.get('discount_percentage')
            max_uses = request.form.get('max_uses')
            is_active = request.form.get('is_active') == 'on'

            if not code or not discount_percentage:
                flash('优惠券代码和折扣百分比不能为空', 'error')
                return redirect(url_for('admin.edit_coupon', coupon_id=coupon_id))

            existing_coupon = Coupon.query.filter(Coupon.code == code, Coupon.id != coupon_id).first()
            if existing_coupon:
                flash('优惠券代码已存在', 'error')
                return redirect(url_for('admin.edit_coupon', coupon_id=coupon_id))

            try:
                discount_val = float(discount_percentage)
                if not (0 < discount_val <= 100):
                    raise ValueError("Discount must be between 0 and 100")
            except ValueError:
                flash('无效的折扣百分比，请输入0到100之间的数字', 'error')
                return redirect(url_for('admin.edit_coupon', coupon_id=coupon_id))

            # 处理最大使用次数
            max_uses_val = None
            if max_uses and max_uses.strip():
                try:
                    max_uses_val = int(max_uses)
                    if max_uses_val <= 0:
                        raise ValueError("Max uses must be positive")
                    # 检查新的最大使用次数是否小于已使用次数
                    if max_uses_val < coupon.used_count:
                        flash(f'最大使用次数不能小于已使用次数 ({coupon.used_count})', 'error')
                        return redirect(url_for('admin.edit_coupon', coupon_id=coupon_id))
                except ValueError:
                    flash('无效的最大使用次数，请输入正整数或留空', 'error')
                    return redirect(url_for('admin.edit_coupon', coupon_id=coupon_id))

            coupon.code = code
            coupon.discount_percentage = discount_val
            coupon.max_uses = max_uses_val
            coupon.is_active = is_active
            db.session.commit()
            flash('优惠券更新成功', 'success')
            return redirect(url_for('admin.list_coupons'))
        except Exception as e:
            db.session.rollback()
            logger.error(f"更新优惠券失败: {e}")
            flash(f'更新优惠券失败: {e}', 'error')
            return redirect(url_for('admin.edit_coupon', coupon_id=coupon_id))

    stats = _get_dashboard_stats()
    return render_template('admin/edit_coupon.html', coupon=coupon, stats=stats)

@admin_bp.route('/coupons/<int:coupon_id>/activate', methods=['POST'])
@admin_required
def activate_coupon(coupon_id):
    """激活优惠券"""
    try:
        coupon = Coupon.query.get_or_404(coupon_id)
        coupon.is_active = True
        db.session.commit()
        flash(f'优惠券 {coupon.code} 已激活', 'success')
    except Exception as e:
        db.session.rollback()
        logger.error(f"激活优惠券失败: {e}")
        flash(f'激活优惠券失败: {e}', 'error')
    return redirect(url_for('admin.list_coupons'))

@admin_bp.route('/coupons/<int:coupon_id>/deactivate', methods=['POST'])
@admin_required
def deactivate_coupon(coupon_id):
    """停用优惠券"""
    try:
        coupon = Coupon.query.get_or_404(coupon_id)
        coupon.is_active = False
        db.session.commit()
        flash(f'优惠券 {coupon.code} 已停用', 'success')
    except Exception as e:
        db.session.rollback()
        logger.error(f"停用优惠券失败: {e}")
        flash(f'停用优惠券失败: {e}', 'error')
    return redirect(url_for('admin.list_coupons'))

@admin_bp.route('/coupons/<int:coupon_id>/delete', methods=['POST'])
@admin_required
def delete_coupon(coupon_id):
    """删除优惠券"""
    try:
        coupon = Coupon.query.get_or_404(coupon_id)
        # Consider if there are any dependencies, e.g., if orders used this coupon,
        # you might want to prevent deletion or handle it gracefully.
        # For now, direct delete.
        db.session.delete(coupon)
        db.session.commit()
        flash(f'优惠券 {coupon.code} 已删除', 'success')
    except Exception as e:
        db.session.rollback()
        logger.error(f"删除优惠券失败: {e}")
        flash(f'删除优惠券失败: {e}', 'error')
    return redirect(url_for('admin.list_coupons'))

# ==================== 订阅管理 ====================

@admin_bp.route('/subscriptions')
@admin_required
def subscriptions():
    """订阅管理页面"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = 20
        user_id = request.args.get('user_id')
        group_id = request.args.get('group_id')

        # 确保只显示有效的订阅（有关联订单的）
        # 明确指定连接条件以避免多外键关系的歧义
        query = Subscription.query.join(
            Order, Subscription.order_id == Order.id
        ).outerjoin(
            User, Order.user_id == User.id
        ).filter(
            Order.id.isnot(None)  # 确保订单存在
        )

        # 过滤条件
        if user_id:
            query = query.filter(Order.user_id == user_id)
        if group_id:
            query = query.filter(Subscription.group_id == group_id)

        subscriptions = query.order_by(Subscription.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        # 获取分组列表用于过滤
        groups = XUIPanelGroup.query.filter_by(is_active=True).all()

        return render_template('admin/subscriptions.html',
                             subscriptions=subscriptions,
                             groups=groups,
                             current_user_id=user_id,
                             current_group_id=group_id)
    except Exception as e:
        logger.error(f"获取订阅列表失败: {e}")
        flash('获取订阅列表失败', 'error')
        return redirect(url_for('admin.dashboard'))

@admin_bp.route('/api/subscriptions/<int:subscription_id>/delete', methods=['DELETE'])
@admin_required
def delete_subscription(subscription_id):
    """删除用户订阅"""
    try:
        logger.info(f"收到删除订阅请求: subscription_id={subscription_id}")
        logger.info(f"请求方法: {request.method}")
        logger.info(f"请求头: {dict(request.headers)}")
        logger.info(f"请求数据: {request.get_data()}")

        subscription = Subscription.query.get_or_404(subscription_id)
        order = subscription.order

        # 记录删除操作
        logger.info(f"管理员 {session['username']} 删除订阅 {subscription_id}，关联订单: {order.order_id}")

        # 从X-UI面板中删除客户端
        deleted_clients = []
        failed_deletions = []

        if order and order.node_configs:
            from xui_client import XUIClient
            from models import XUIPanel

            # 获取订阅关联的分组
            group = subscription.group
            if group:
                # 获取分组中的所有面板
                panels = group.active_panels
                logger.info(f"订阅关联分组 {group.name}，包含 {len(panels)} 个活跃面板")

                for node_config in order.node_configs:
                    if node_config.is_active and node_config.client_email:
                        # 尝试从分组中的每个面板删除客户端
                        client_deleted = False
                        for panel in panels:
                            try:
                                # 创建XUI客户端
                                client = XUIClient(
                                    base_url=panel.base_url,
                                    username=panel.username,
                                    password=panel.password,
                                    path_prefix=panel.path_prefix
                                )

                                # 尝试从X-UI面板删除客户端
                                success, error_msg = client.delete_client_by_email(node_config.client_email)
                                if success:
                                    deleted_clients.append(f"{node_config.client_email} (面板: {panel.name})")
                                    logger.info(f"成功从面板 {panel.name} 删除客户端: {node_config.client_email}")
                                    client_deleted = True
                                    break  # 成功删除后跳出循环
                                else:
                                    logger.warning(f"从面板 {panel.name} 删除客户端失败: {node_config.client_email} - {error_msg}")

                            except Exception as e:
                                logger.error(f"从面板 {panel.name} 删除客户端 {node_config.client_email} 时发生错误: {e}")

                        if not client_deleted:
                            failed_deletions.append(f"{node_config.client_email}: 在所有面板中都删除失败")

                    # 停用节点配置
                    node_config.is_active = False
                    logger.info(f"停用节点配置 {node_config.id}")
            else:
                # 如果没有关联分组，尝试从所有活跃面板删除客户端
                logger.warning(f"订阅 {subscription_id} 没有关联分组，尝试从所有活跃面板删除客户端")
                all_panels = XUIPanel.query.filter_by(status=PanelStatus.ACTIVE).all()
                logger.info(f"找到 {len(all_panels)} 个活跃面板")

                for node_config in order.node_configs:
                    if node_config.is_active and node_config.client_email:
                        # 尝试从所有面板删除客户端
                        client_deleted = False
                        for panel in all_panels:
                            try:
                                # 创建XUI客户端
                                client = XUIClient(
                                    base_url=panel.base_url,
                                    username=panel.username,
                                    password=panel.password,
                                    path_prefix=panel.path_prefix
                                )

                                # 尝试从X-UI面板删除客户端
                                success, error_msg = client.delete_client_by_email(node_config.client_email)
                                if success:
                                    deleted_clients.append(f"{node_config.client_email} (面板: {panel.name})")
                                    logger.info(f"成功从面板 {panel.name} 删除客户端: {node_config.client_email}")
                                    client_deleted = True
                                    break  # 成功删除后跳出循环
                                else:
                                    logger.warning(f"从面板 {panel.name} 删除客户端失败: {node_config.client_email} - {error_msg}")

                            except Exception as e:
                                logger.error(f"从面板 {panel.name} 删除客户端 {node_config.client_email} 时发生错误: {e}")

                        if not client_deleted:
                            failed_deletions.append(f"{node_config.client_email}: 在所有面板中都删除失败")

                    # 停用节点配置
                    node_config.is_active = False
                    logger.info(f"停用节点配置 {node_config.id}")

        # 检查是否要硬删除
        hard_delete = request.args.get('hard_delete', 'false').lower() == 'true'

        if hard_delete:
            # 硬删除：从数据库中完全删除
            logger.info(f"执行硬删除：删除订阅 {subscription_id} 和关联订单 {order.order_id}")

            # 先处理流量基准数据删除
            from services.traffic_baseline_deletion_service import traffic_baseline_deletion_service
            baseline_result = traffic_baseline_deletion_service.handle_subscription_deletion(subscription_id)
            if baseline_result['success']:
                logger.info(f"流量基准处理成功: {baseline_result.get('message', '')}")
            else:
                logger.warning(f"流量基准处理失败: {baseline_result.get('error', '')}")

            # 先手动删除关联的流量统计记录以避免约束冲突
            from models import TrafficStats
            traffic_stats_count = TrafficStats.query.filter_by(subscription_id=subscription_id).count()
            if traffic_stats_count > 0:
                TrafficStats.query.filter_by(subscription_id=subscription_id).delete()
                logger.info(f"删除了 {traffic_stats_count} 条关联的流量统计记录")

            # 先手动删除关联的续费任务记录
            from models import RenewalTask
            renewal_tasks_count = RenewalTask.query.filter_by(subscription_id=subscription_id).count()
            if renewal_tasks_count > 0:
                RenewalTask.query.filter_by(subscription_id=subscription_id).delete()
                logger.info(f"删除了 {renewal_tasks_count} 条关联的续费任务记录")

            db.session.delete(subscription)
            db.session.delete(order)
            delete_type = "硬删除"
        else:
            # 软删除：设置为非活跃状态
            subscription.is_active = False
            subscription.updated_at = datetime.utcnow()
            delete_type = "软删除"

        db.session.commit()

        # 构建响应消息
        message_parts = [f'订阅已{delete_type}，关联订单: {order.order_id}']

        if deleted_clients:
            message_parts.append(f'成功从X-UI面板删除 {len(deleted_clients)} 个客户端')

        if failed_deletions:
            message_parts.append(f'有 {len(failed_deletions)} 个客户端删除失败')
            logger.warning(f"删除失败的客户端: {failed_deletions}")

        return jsonify({
            'success': True,
            'message': '; '.join(message_parts),
            'details': {
                'deleted_clients': deleted_clients,
                'failed_deletions': failed_deletions
            }
        })

    except Exception as e:
        logger.error(f"删除订阅失败: {e}")
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'删除订阅失败: {str(e)}'
        }), 500

# ==================== 数据清理管理 ====================
# 注意：数据清理现在自动化处理，不再需要手动API接口
# 当管理员删除面板或从分组中移除面板时，系统会自动清理孤儿数据

# ==================== 到期订阅管理 ====================

@admin_bp.route('/api/subscriptions/cleanup-expired', methods=['POST'])
@admin_required
def cleanup_expired_subscriptions():
    """手动清理到期订阅"""
    try:
        from services.scheduler_service import scheduler_service
        
        logger.info(f"管理员 {session['username']} 手动触发到期订阅清理")
        
        # 触发到期订阅清理
        result = scheduler_service.trigger_expired_subscriptions_cleanup()
        
        if result['success']:
            stats = result['stats']
            message_parts = [f"到期订阅清理完成"]
            
            if stats['total_expired'] > 0:
                message_parts.append(f"处理了 {stats['total_expired']} 个到期订阅")
                message_parts.append(f"成功删除 {stats['successfully_deleted']} 个")
                
                if stats['failed_deletions'] > 0:
                    message_parts.append(f"失败 {stats['failed_deletions']} 个")
            else:
                message_parts.append("没有找到到期的订阅")
            
            return jsonify({
                'success': True,
                'message': '; '.join(message_parts),
                'stats': stats
            })
        else:
            return jsonify({
                'success': False,
                'message': f"清理失败: {result.get('error', '未知错误')}"
            }), 500
            
    except Exception as e:
        logger.error(f"手动清理到期订阅失败: {e}")
        return jsonify({
            'success': False,
            'message': f'清理失败: {str(e)}'
        }), 500

@admin_bp.route('/api/subscriptions/expiring-soon')
@admin_required
def get_expiring_soon_subscriptions():
    """获取即将到期的订阅列表"""
    try:
        from services.expiration_service import expiration_service
        
        days = request.args.get('days', 7, type=int)
        
        expiring_subscriptions = expiration_service.get_expiring_soon_subscriptions(days)
        
        return jsonify({
            'success': True,
            'data': expiring_subscriptions,
            'total': len(expiring_subscriptions)
        })
        
    except Exception as e:
        logger.error(f"获取即将到期订阅失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取失败: {str(e)}'
        }), 500

@admin_bp.route('/api/subscriptions/<int:subscription_id>/force-delete', methods=['DELETE'])
@admin_required
def force_delete_subscription(subscription_id):
    """强制删除指定订阅（包括X-UI面板客户端）"""
    try:
        from services.expiration_service import expiration_service
        
        logger.info(f"管理员 {session['username']} 强制删除订阅 {subscription_id}")
        
        result = expiration_service.force_delete_subscription(subscription_id)
        
        if result['success']:
            message_parts = [f"订阅 {subscription_id} 已强制删除"]
            
            if result.get('deleted_clients'):
                message_parts.append(f"成功从X-UI面板删除 {len(result['deleted_clients'])} 个客户端")
                
            if result.get('failed_client_deletions'):
                message_parts.append(f"有 {len(result['failed_client_deletions'])} 个客户端删除失败")
            
            return jsonify({
                'success': True,
                'message': '; '.join(message_parts),
                'details': result
            })
        else:
            return jsonify({
                'success': False,
                'message': result.get('error', '删除失败')
            }), 500
            
    except Exception as e:
        logger.error(f"强制删除订阅失败: {e}")
        return jsonify({
            'success': False,
            'message': f'删除失败: {str(e)}'
        }), 500

# ==================== 流量统计管理 ====================
# 流量统计功能已被移除

@admin_bp.route('/api/scheduler/status')
@admin_required
def get_scheduler_status():
    """获取定时任务状态"""
    try:
        from services.scheduler_service import scheduler_service

        status = scheduler_service.get_job_status()

        return jsonify({
            'success': True,
            'data': status
        })

    except Exception as e:
        logger.error(f"获取定时任务状态失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取状态失败: {str(e)}'
        }), 500

# ==================== 邮件配置管理 ====================

@admin_bp.route('/email-config')
@admin_required
def email_config():
    """邮件配置管理页面（单配置模式）"""
    try:
        from services.notification_service import notification_service

        # 获取唯一的邮件配置
        config = EmailConfig.get_single_config()

        # 获取统计信息
        stats = notification_service.get_notification_stats()

        return render_template('admin/email_config.html', config=config, stats=stats)
    except Exception as e:
        logger.error(f"获取邮件配置失败: {e}")
        flash('获取邮件配置失败', 'error')
        return redirect(url_for('admin.dashboard'))

@admin_bp.route('/scheduler-monitor')
@admin_required
def scheduler_monitor():
    """定时任务监控页面"""
    return render_template('admin/scheduler_monitor.html')

@admin_bp.route('/api/email-config', methods=['GET'])
@admin_required
def get_email_config():
    """获取唯一的邮件配置（单配置模式）"""
    try:
        config = EmailConfig.get_single_config()

        if config:
            return jsonify({
                'success': True,
                'data': config.to_dict()
            })
        else:
            return jsonify({
                'success': True,
                'data': None,
                'message': '暂无邮件配置'
            })

    except Exception as e:
        logger.error(f"获取邮件配置失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取失败: {str(e)}'
        }), 500

@admin_bp.route('/api/email-config', methods=['POST'])
@admin_required
def create_or_update_email_config():
    """创建或更新邮件配置（单配置模式）"""
    try:
        # 获取表单数据
        name = request.form.get('name')
        smtp_server = request.form.get('smtp_server')
        smtp_port = int(request.form.get('smtp_port', 587))
        username = request.form.get('username')
        password = request.form.get('password')

        if not all([name, smtp_server, username]):
            return jsonify({
                'success': False,
                'message': '请填写所有必需字段'
            }), 400

        # 处理加密方式
        encryption = request.form.get('encryption', 'tls')
        use_tls = encryption == 'tls'
        use_ssl = encryption == 'ssl'

        # 准备配置数据
        config_data = {
            'name': name,
            'smtp_server': smtp_server,
            'smtp_port': smtp_port,
            'use_tls': use_tls,
            'use_ssl': use_ssl,
            'username': username,
            'default_sender': request.form.get('default_sender') or username,
            'sender_name': request.form.get('sender_name', '系统通知'),
            'daily_limit': int(request.form.get('daily_limit', 1000)),
            'rate_limit_per_minute': int(request.form.get('rate_limit_per_minute', 10)),
            'is_active': request.form.get('is_active') == 'on'
        }

        # 只有提供了密码才更新密码
        if password:
            config_data['password'] = password

        # 创建或更新配置
        config = EmailConfig.create_or_update_config(**config_data)

        # 刷新邮件服务配置缓存
        from services.email_service import email_service
        email_service.refresh_config()

        logger.info(f"管理员 {session['username']} 创建/更新邮件配置: {name}")

        return jsonify({
            'success': True,
            'message': '邮件配置保存成功',
            'data': config.to_dict()
        })

    except Exception as e:
        logger.error(f"保存邮件配置失败: {e}")
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'保存失败: {str(e)}'
        }), 500

# 单配置模式下，移除了以下不需要的路由：
# - GET /api/email-config/<int:config_id> (获取特定配置详情)
# - PUT /api/email-config/<int:config_id> (更新特定配置)
# - DELETE /api/email-config/<int:config_id> (删除特定配置)
# - POST /api/email-config/<int:config_id>/set-default (设置默认配置)
#
# 单配置模式下，所有操作都通过 POST /api/email-config 进行创建或更新

@admin_bp.route('/api/email-config/test', methods=['POST'])
@admin_required
def test_email_config():
    """测试邮件配置连接"""
    try:
        import smtplib

        # 从表单数据获取配置
        smtp_server = request.form.get('smtp_server')
        smtp_port = int(request.form.get('smtp_port', 587))
        username = request.form.get('username')
        password = request.form.get('password')
        encryption = request.form.get('encryption', 'tls')

        if not all([smtp_server, username, password]):
            return jsonify({
                'success': False,
                'message': '请填写完整的SMTP配置信息'
            }), 400

        # 如果密码是占位符，尝试从数据库获取
        if password == 'test' or not password:
            # 获取现有配置
            existing_config = EmailConfig.get_single_config()
            if existing_config and existing_config.smtp_server == smtp_server and existing_config.username == username:
                password = existing_config.password
            else:
                return jsonify({
                    'success': False,
                    'message': '无法获取存储的密码，请提供完整的配置信息'
                }), 400

        # 测试SMTP连接
        try:
            if encryption == 'ssl':
                server = smtplib.SMTP_SSL(smtp_server, smtp_port)
            else:
                server = smtplib.SMTP(smtp_server, smtp_port)
                if encryption == 'tls':
                    server.starttls()

            server.login(username, password)
            server.quit()

            return jsonify({
                'success': True,
                'message': '邮件服务器连接成功'
            })

        except Exception as e:
            # 创建临时配置对象用于诊断
            from models.email_config import EmailConfig
            temp_config = EmailConfig()
            temp_config.smtp_server = smtp_server
            temp_config.smtp_port = smtp_port
            temp_config.username = username
            temp_config.use_tls = encryption == 'tls'
            temp_config.use_ssl = encryption == 'ssl'
            
            from services.email_service import email_service
            diagnostic_info = email_service._diagnose_smtp_error(e, temp_config)
            
            return jsonify({
                'success': False,
                'message': f'连接失败: {str(e)}',
                'diagnostic': diagnostic_info
            })

    except Exception as e:
        logger.error(f"测试邮件配置失败: {e}")
        return jsonify({
            'success': False,
            'message': f'测试失败: {str(e)}'
        }), 500

@admin_bp.route('/api/email-config/send-test', methods=['POST'])
@admin_required
def send_test_email():
    """发送测试邮件"""
    try:
        from services.email_service import email_service

        test_email = request.form.get('test_email')
        test_subject = request.form.get('test_subject', '邮件配置测试')
        test_content = request.form.get('test_content', '这是一封测试邮件，用于验证邮件配置是否正常工作。')

        if not test_email:
            return jsonify({
                'success': False,
                'message': '请输入收件人邮箱'
            }), 400

        # 发送测试邮件
        success = email_service.send_email(test_email, test_subject, test_content)

        if success:
            logger.info(f"管理员 {session['username']} 发送测试邮件到: {test_email}")
            return jsonify({
                'success': True,
                'message': f'测试邮件已发送到 {test_email}'
            })
        else:
            # 获取邮件配置以提供诊断信息
            from models.email_config import EmailConfig
            config = EmailConfig.get_single_config()
            diagnostic_msg = ""
            if config:
                # 模拟一个常见的认证错误来提供诊断建议
                try:
                    import smtplib
                    server = smtplib.SMTP(config.smtp_server, config.smtp_port)
                    if config.use_tls:
                        server.starttls()
                    server.login(config.username, config.password)
                    server.quit()
                except Exception as test_error:
                    diagnostic_msg = email_service._diagnose_smtp_error(test_error, config)
            
            return jsonify({
                'success': False,
                'message': '测试邮件发送失败',
                'diagnostic': diagnostic_msg
            }), 500

    except Exception as e:
        logger.error(f"发送测试邮件失败: {e}")
        return jsonify({
            'success': False,
            'message': f'发送失败: {str(e)}'
        }), 500

@admin_bp.route('/api/email-config/refresh', methods=['POST'])
@admin_required
def refresh_email_config():
    """刷新邮件配置缓存"""
    try:
        from services.email_service import email_service

        email_service.refresh_config()

        logger.info(f"管理员 {session['username']} 刷新邮件配置缓存")

        return jsonify({
            'success': True,
            'message': '邮件配置缓存已刷新'
        })

    except Exception as e:
        logger.error(f"刷新邮件配置缓存失败: {e}")
        return jsonify({
            'success': False,
            'message': f'刷新失败: {str(e)}'
        }), 500

@admin_bp.route('/api/email-config/validate', methods=['POST'])
@admin_required
def validate_email_config():
    """验证邮件配置并提供建议"""
    try:
        from models.email_config import EmailConfig
        
        config = EmailConfig.get_single_config()
        if not config:
            return jsonify({
                'success': False,
                'message': '未找到邮件配置'
            }), 404
        
        # 检查配置并提供建议
        suggestions = []
        warnings = []
        
        # 检查QQ邮箱配置
        if 'qq.com' in config.smtp_server.lower() or '@qq.com' in config.username.lower():
            if config.smtp_server != 'smtp.qq.com':
                warnings.append('QQ邮箱建议使用 smtp.qq.com 作为SMTP服务器')
            
            if config.smtp_port not in [587, 465]:
                warnings.append('QQ邮箱建议使用端口 587(TLS) 或 465(SSL)')
            
            if config.smtp_port == 587 and not config.use_tls:
                warnings.append('端口587建议启用TLS加密')
            
            if config.smtp_port == 465 and not config.use_ssl:
                warnings.append('端口465建议启用SSL加密')
            
            suggestions.append('QQ邮箱需要使用授权码而非登录密码')
            suggestions.append('请确保在QQ邮箱设置中已开启IMAP/SMTP服务')
        
        # 检查Gmail配置
        elif 'gmail.com' in config.smtp_server.lower() or '@gmail.com' in config.username.lower():
            if config.smtp_server != 'smtp.gmail.com':
                warnings.append('Gmail建议使用 smtp.gmail.com 作为SMTP服务器')
            
            suggestions.append('Gmail需要使用应用专用密码')
            suggestions.append('请确保已启用两步验证')
        
        # 检查通用配置
        if not config.use_tls and not config.use_ssl:
            warnings.append('建议启用TLS或SSL加密以确保安全')
        
        return jsonify({
            'success': True,
            'config': config.to_dict(),
            'suggestions': suggestions,
            'warnings': warnings,
            'message': '配置验证完成'
        })
        
    except Exception as e:
        logger.error(f"验证邮件配置失败: {e}")
        return jsonify({
            'success': False,
            'message': f'验证失败: {str(e)}'
        }), 500

# ==================== 续费配置管理 ====================

@admin_bp.route('/renewal-config')
@admin_required
def renewal_config():
    """续费配置管理页面"""
    try:
        # 获取所有续费价格配置
        pricing_configs = RenewalPricing.query.order_by(RenewalPricing.duration_months.asc()).all()

        # 如果没有配置，创建默认配置
        if not pricing_configs:
            RenewalPricing.create_default_pricing()
            pricing_configs = RenewalPricing.query.order_by(RenewalPricing.duration_months.asc()).all()

        logger.info(f"获取到 {len(pricing_configs)} 个续费配置")
        return render_template('admin/renewal_config.html', pricing_configs=pricing_configs)
    except ImportError as e:
        logger.error(f"导入错误: {e}")
        flash('续费功能模块加载失败', 'error')
        return redirect(url_for('admin.dashboard'))
    except Exception as e:
        logger.error(f"获取续费配置失败: {e}")
        flash(f'获取续费配置失败: {str(e)}', 'error')
        return redirect(url_for('admin.dashboard'))

@admin_bp.route('/api/renewal-config', methods=['GET', 'POST'])
@admin_required
def api_renewal_config():
    """续费配置API"""
    if request.method == 'GET':
        try:
            pricing_configs = RenewalPricing.query.order_by(RenewalPricing.duration_months.asc()).all()
            return jsonify({
                'success': True,
                'data': [config.to_dict() for config in pricing_configs]
            })
        except Exception as e:
            logger.error(f"获取续费配置API失败: {e}")
            return jsonify({
                'success': False,
                'message': f'获取配置失败: {str(e)}'
            }), 500

    elif request.method == 'POST':
        try:
            data = request.get_json()
            if not data:
                return jsonify({
                    'success': False,
                    'message': '请求数据不能为空'
                }), 400

            # 更新或创建配置
            for config_data in data.get('configs', []):
                duration_months = config_data.get('duration_months')
                discount_percentage = config_data.get('discount_percentage', 0.0)
                is_active = config_data.get('is_active', True)

                if not duration_months:
                    continue

                # 查找现有配置
                existing_config = RenewalPricing.query.filter_by(duration_months=duration_months).first()

                if existing_config:
                    # 更新现有配置
                    existing_config.discount_percentage = discount_percentage
                    existing_config.is_active = is_active
                    existing_config.updated_at = datetime.utcnow()
                else:
                    # 创建新配置
                    new_config = RenewalPricing(
                        duration_months=duration_months,
                        discount_percentage=discount_percentage,
                        is_active=is_active
                    )
                    db.session.add(new_config)

            db.session.commit()

            logger.info(f"管理员 {session['username']} 更新续费配置")

            return jsonify({
                'success': True,
                'message': '续费配置保存成功'
            })

        except Exception as e:
            logger.error(f"保存续费配置失败: {e}")
            db.session.rollback()
            return jsonify({
                'success': False,
                'message': f'保存失败: {str(e)}'
            }), 500

@admin_bp.route('/renewal-tasks')
@admin_required
def renewal_tasks():
    """续费任务管理页面"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = 20

        # 获取续费任务列表
        tasks = RenewalTask.query.order_by(RenewalTask.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        return render_template('admin/renewal_tasks.html', tasks=tasks)
    except Exception as e:
        logger.error(f"获取续费任务列表失败: {e}")
        flash('获取续费任务列表失败', 'error')
        return redirect(url_for('admin.dashboard'))

@admin_bp.route('/api/renewal-tasks/<int:task_id>/retry', methods=['POST'])
@admin_required
def retry_renewal_task(task_id):
    """重试续费任务"""
    try:
        task = RenewalTask.query.get_or_404(task_id)

        if not task.can_retry():
            return jsonify({
                'success': False,
                'message': '任务不能重试（已完成或超过最大重试次数）'
            }), 400

        # 重试逻辑（这里先简单标记，具体实现在RenewalService中）
        from services.renewal_service import RenewalService
        renewal_service = RenewalService()

        # TODO: 实现重试逻辑
        task.increment_retry()
        db.session.commit()

        logger.info(f"管理员 {session['username']} 重试续费任务 {task_id}")

        return jsonify({
            'success': True,
            'message': '重试任务已提交'
        })

    except Exception as e:
        logger.error(f"重试续费任务失败: {e}")
        return jsonify({
            'success': False,
            'message': f'重试失败: {str(e)}'
        }), 500

# ==================== 流量数据优化管理 ====================

@admin_bp.route('/traffic-optimization')
@admin_required
def traffic_optimization():
    """流量数据优化管理页面"""
    try:
        from services.traffic_data_cleanup_service import traffic_data_cleanup_service

        # 获取当前统计信息
        stats = traffic_data_cleanup_service.get_current_stats()

        # 获取清理预览
        preview = traffic_data_cleanup_service.get_cleanup_preview()

        return render_template('admin/traffic_optimization.html',
                             stats=stats,
                             preview=preview)
    except Exception as e:
        logger.error(f"获取流量优化页面数据失败: {e}")
        flash('获取流量优化数据失败', 'error')
        return redirect(url_for('admin.dashboard'))

@admin_bp.route('/api/traffic-optimization/stats')
@admin_required
def get_traffic_optimization_stats():
    """获取流量数据优化统计信息API"""
    try:
        from services.traffic_data_cleanup_service import traffic_data_cleanup_service

        stats = traffic_data_cleanup_service.get_current_stats()

        return jsonify({
            'success': True,
            'stats': stats
        })

    except Exception as e:
        logger.error(f"获取流量优化统计失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@admin_bp.route('/api/traffic-optimization/preview')
@admin_required
def get_traffic_cleanup_preview():
    """获取流量数据清理预览API"""
    try:
        from services.traffic_data_cleanup_service import traffic_data_cleanup_service

        preview = traffic_data_cleanup_service.get_cleanup_preview()

        return jsonify({
            'success': True,
            'preview': preview
        })

    except Exception as e:
        logger.error(f"获取流量清理预览失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@admin_bp.route('/api/traffic-optimization/cleanup', methods=['POST'])
@admin_required
def execute_traffic_cleanup():
    """执行流量数据清理API"""
    try:
        from services.traffic_data_cleanup_service import traffic_data_cleanup_service

        logger.info(f"管理员 {session['username']} 手动执行流量数据清理")

        result = traffic_data_cleanup_service.cleanup_old_traffic_data()

        if result['success']:
            logger.info(f"手动流量数据清理完成: {result['message']}")
        else:
            logger.error(f"手动流量数据清理失败: {result.get('message', '未知错误')}")

        return jsonify(result)

    except Exception as e:
        logger.error(f"执行流量数据清理失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': f'清理失败: {str(e)}'
        }), 500
