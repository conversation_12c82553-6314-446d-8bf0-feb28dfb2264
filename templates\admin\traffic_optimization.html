{% extends "base.html" %}

{% block title %}流量数据优化{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>
                    <i class="bi bi-speedometer2"></i> 流量数据优化
                </h1>
                <div>
                    <button type="button" class="btn btn-info btn-sm" onclick="refreshStats()">
                        <i class="bi bi-arrow-clockwise"></i> 刷新统计
                    </button>
                    <button type="button" class="btn btn-warning btn-sm" onclick="previewCleanup()">
                        <i class="bi bi-eye"></i> 预览清理
                    </button>
                    <button type="button" class="btn btn-danger btn-sm" onclick="executeCleanup()">
                        <i class="bi bi-trash"></i> 执行清理
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">流量数据优化管理</h5>
                </div>
                <div class="card-body">
                    <!-- 优化说明 -->
                    <div class="alert alert-info">
                        <h5><i class="bi bi-info-circle"></i> 流量数据优化说明</h5>
                        <p class="mb-2">
                            <strong>优化原理：</strong>系统每5分钟收集一次流量数据，长期运行会产生大量记录。
                            优化方案保留最近 <span class="badge bg-primary">{{ stats.retention_days if stats.success else 30 }}</span> 天的详细数据，
                            自动清理更早的历史数据。
                        </p>
                        <p class="mb-0">
                            <strong>优化效果：</strong>减少数据库大小，提高查询性能，不影响任何业务功能。
                        </p>
                    </div>

                    <!-- 当前统计 -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 id="total-records">{{ stats.total_records if stats.success else 'N/A' }}</h4>
                                            <p class="mb-0">总流量记录</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-database" style="font-size: 2rem;"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 id="recent-records">{{ stats.recent_records if stats.success else 'N/A' }}</h4>
                                            <p class="mb-0">保留记录</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-check-circle" style="font-size: 2rem;"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 id="old-records">{{ stats.old_records if stats.success else 'N/A' }}</h4>
                                            <p class="mb-0">可清理记录</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-trash" style="font-size: 2rem;"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 id="optimization-ratio">{{ stats.optimization_ratio if stats.success else 'N/A' }}</h4>
                                            <p class="mb-0">优化比例</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-percent" style="font-size: 2rem;"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 时间范围信息 -->
                    {% if stats.success %}
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="bi bi-calendar"></i> 数据时间范围</h5>
                                </div>
                                <div class="card-body">
                                    <p><strong>最早记录：</strong> 
                                        <span id="earliest-record">{{ stats.earliest_record or '无数据' }}</span>
                                    </p>
                                    <p><strong>最新记录：</strong> 
                                        <span id="latest-record">{{ stats.latest_record or '无数据' }}</span>
                                    </p>
                                    <p><strong>清理截止时间：</strong> 
                                        <span id="cutoff-date">{{ stats.cutoff_date or '无数据' }}</span>
                                    </p>
                                    <p class="mb-0"><strong>保留天数：</strong> 
                                        <span id="retention-days" class="badge bg-primary">{{ stats.retention_days }} 天</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="bi bi-pie-chart"></i> 清理预览</h5>
                                </div>
                                <div class="card-body" id="cleanup-preview">
                                    {% if preview.success %}
                                        {% if preview.will_delete_count > 0 %}
                                        <p><strong>将清理记录：</strong> 
                                            <span class="badge bg-warning">{{ preview.will_delete_count }} 条</span>
                                        </p>
                                        <p><strong>释放空间：</strong> 
                                            <span class="badge bg-info">{{ "%.2f"|format(preview.total_mb_to_free) }} MB</span>
                                        </p>
                                        <p class="mb-0 text-success">
                                            <i class="bi bi-check"></i> {{ preview.message }}
                                        </p>
                                        {% else %}
                                        <p class="text-success mb-0">
                                            <i class="bi bi-check-circle"></i> {{ preview.message }}
                                        </p>
                                        {% endif %}
                                    {% else %}
                                    <p class="text-danger mb-0">
                                        <i class="bi bi-exclamation-triangle"></i> 预览失败：{{ preview.message }}
                                    </p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- 操作日志 -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-clock-history"></i> 操作日志</h5>
                        </div>
                        <div class="card-body">
                            <div id="operation-log" style="max-height: 300px; overflow-y: auto;">
                                <p class="text-muted">暂无操作记录</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 返回按钮 -->
    <div class="row mt-4">
        <div class="col-12">
            <a href="{{ url_for('admin.dashboard') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回仪表板
            </a>
        </div>
    </div>
</div>

<!-- 确认清理模态框 -->
<div class="modal fade" id="confirmCleanupModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认执行流量数据清理</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>注意：</strong>此操作将删除超过保留期的流量统计数据，删除后无法恢复。
                </div>
                <p id="cleanup-confirmation-text">确定要执行清理操作吗？</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="confirmCleanup()">确认清理</button>
            </div>
        </div>
    </div>
</div>

<script>
// 刷新统计信息
function refreshStats() {
    showLoading('正在刷新统计信息...');
    
    fetch('/admin/api/traffic-optimization/stats')
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                updateStatsDisplay(data.stats);
                addLog('success', '统计信息刷新成功');
            } else {
                addLog('error', '刷新统计信息失败: ' + data.error);
            }
        })
        .catch(error => {
            hideLoading();
            addLog('error', '刷新统计信息失败: ' + error.message);
        });
}

// 预览清理
function previewCleanup() {
    showLoading('正在生成清理预览...');
    
    fetch('/admin/api/traffic-optimization/preview')
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                updatePreviewDisplay(data.preview);
                addLog('info', '清理预览生成成功');
            } else {
                addLog('error', '生成清理预览失败: ' + data.error);
            }
        })
        .catch(error => {
            hideLoading();
            addLog('error', '生成清理预览失败: ' + error.message);
        });
}

// 执行清理
function executeCleanup() {
    // 先获取预览信息
    fetch('/admin/api/traffic-optimization/preview')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.preview.will_delete_count > 0) {
                const confirmText = `将清理 ${data.preview.will_delete_count} 条记录，释放约 ${data.preview.total_mb_to_free.toFixed(2)} MB 空间。`;
                document.getElementById('cleanup-confirmation-text').textContent = confirmText;
                new bootstrap.Modal(document.getElementById('confirmCleanupModal')).show();
            } else if (data.success && data.preview.will_delete_count === 0) {
                addLog('info', '没有需要清理的数据');
            } else {
                addLog('error', '获取清理预览失败');
            }
        })
        .catch(error => {
            addLog('error', '获取清理预览失败: ' + error.message);
        });
}

// 确认清理
function confirmCleanup() {
    const modal = bootstrap.Modal.getInstance(document.getElementById('confirmCleanupModal'));
    modal.hide();
    
    showLoading('正在执行流量数据清理...');
    
    fetch('/admin/api/traffic-optimization/cleanup', {
        method: 'POST'
    })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                addLog('success', data.message);
                // 刷新统计信息
                setTimeout(refreshStats, 1000);
            } else {
                addLog('error', '清理失败: ' + data.message);
            }
        })
        .catch(error => {
            hideLoading();
            addLog('error', '清理失败: ' + error.message);
        });
}

// 更新统计显示
function updateStatsDisplay(stats) {
    if (stats.success) {
        document.getElementById('total-records').textContent = stats.total_records;
        document.getElementById('recent-records').textContent = stats.recent_records;
        document.getElementById('old-records').textContent = stats.old_records;
        document.getElementById('optimization-ratio').textContent = stats.optimization_ratio;
        document.getElementById('earliest-record').textContent = stats.earliest_record || '无数据';
        document.getElementById('latest-record').textContent = stats.latest_record || '无数据';
        document.getElementById('cutoff-date').textContent = stats.cutoff_date || '无数据';
        document.getElementById('retention-days').textContent = stats.retention_days + ' 天';
    }
}

// 更新预览显示
function updatePreviewDisplay(preview) {
    const previewDiv = document.getElementById('cleanup-preview');
    if (preview.success) {
        if (preview.will_delete_count > 0) {
            previewDiv.innerHTML = `
                <p><strong>将清理记录：</strong> 
                    <span class="badge bg-warning">${preview.will_delete_count} 条</span>
                </p>
                <p><strong>释放空间：</strong> 
                    <span class="badge bg-info">${preview.total_mb_to_free.toFixed(2)} MB</span>
                </p>
                <p class="mb-0 text-success">
                    <i class="fas fa-check"></i> ${preview.message}
                </p>
            `;
        } else {
            previewDiv.innerHTML = `
                <p class="text-success mb-0">
                    <i class="fas fa-check-circle"></i> ${preview.message}
                </p>
            `;
        }
    } else {
        previewDiv.innerHTML = `
            <p class="text-danger mb-0">
                <i class="fas fa-exclamation-triangle"></i> 预览失败：${preview.message}
            </p>
        `;
    }
}

// 添加日志
function addLog(type, message) {
    const logDiv = document.getElementById('operation-log');
    const timestamp = new Date().toLocaleString();
    const iconClass = type === 'success' ? 'bi-check-circle text-success' :
                     type === 'error' ? 'bi-exclamation-circle text-danger' :
                     'bi-info-circle text-info';
    
    const logEntry = document.createElement('div');
    logEntry.className = 'mb-2';
    logEntry.innerHTML = `
        <small class="text-muted">${timestamp}</small>
        <i class="bi ${iconClass} ms-2"></i>
        <span class="ms-1">${message}</span>
    `;
    
    // 如果是第一条日志，清除"暂无操作记录"
    if (logDiv.querySelector('.text-muted') && logDiv.children.length === 1) {
        logDiv.innerHTML = '';
    }
    
    logDiv.insertBefore(logEntry, logDiv.firstChild);
    
    // 限制日志条数
    while (logDiv.children.length > 20) {
        logDiv.removeChild(logDiv.lastChild);
    }
}

// 显示加载状态
function showLoading(message) {
    // 简单的加载提示
    console.log('Loading:', message);
}

// 隐藏加载状态
function hideLoading() {
    console.log('Loading finished');
}

// 页面加载完成后自动刷新一次统计
document.addEventListener('DOMContentLoaded', function() {
    // 如果统计数据加载失败，自动刷新一次
    {% if not stats.success %}
    setTimeout(refreshStats, 1000);
    {% endif %}
});
</script>
{% endblock %}
