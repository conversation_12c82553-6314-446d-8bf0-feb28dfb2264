<!DOCTYPE html>
<html>
<head>
    <title>最小化测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container py-4">
        <h1>最小化JavaScript测试</h1>
        <button class="btn btn-primary" onclick="testFunction()">测试函数</button>
        <div id="result" class="mt-3"></div>
    </div>
    
    <script>
        function showLoading(message) {
            console.log('showLoading:', message);
            document.getElementById('result').innerHTML = `<div class="alert alert-info">${message}</div>`;
        }
        
        function hideLoading() {
            console.log('hideLoading called');
            document.getElementById('result').innerHTML = '<div class="alert alert-success">加载完成</div>';
        }
        
        function testFunction() {
            console.log('testFunction: 开始');
            showLoading('正在测试...');
            
            setTimeout(() => {
                console.log('testFunction: 模拟API调用完成');
                hideLoading();
            }, 2000);
        }
        
        console.log('JavaScript加载完成');
    </script>
</body>
</html>