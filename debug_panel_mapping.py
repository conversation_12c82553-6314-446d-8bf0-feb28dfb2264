#!/usr/bin/env python3
"""
调试面板映射问题
"""
from app import create_app
from services.config_service import ConfigService

def main():
    app = create_app()
    with app.app_context():
        print('=== 调试配置服务面板映射 ===\n')
        
        config_service = ConfigService()
        panels_config = config_service.get_xui_panels()
        
        print(f'配置服务中的面板数量: {len(panels_config)}')
        for panel_key, panel_config in panels_config.items():
            print(f'  面板键: {panel_key}')
            print(f'    名称: {panel_config.get("name")}')
            print(f'    数据库ID: {panel_config.get("panel_id")}')
            print(f'    base_url: {panel_config.get("base_url")}')
            print()

if __name__ == '__main__':
    main()
