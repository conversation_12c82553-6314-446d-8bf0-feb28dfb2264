#!/usr/bin/env python3
"""
测试批量订阅同步功能
"""
from app import create_app
from models import db, Subscription, Order, XUIPanelGroup, XUIPanel
from services.subscription_sync_service import SubscriptionSyncService
import uuid

def main():
    app = create_app()
    with app.app_context():
        print('=== 测试批量订阅同步功能 ===\n')
        
        # 1. 检查系统状态
        print('1. 系统状态检查:')
        
        # 检查分组和面板
        groups = XUIPanelGroup.query.all()
        print(f'   面板分组数量: {len(groups)}')
        
        for group in groups:
            panels = group.panels
            print(f'   分组: {group.name} (ID: {group.id})')
            print(f'     面板数量: {len(panels)}')
            for panel in panels:
                print(f'       - {panel.name} ({panel.base_url})')
        
        # 检查活跃订阅
        active_subscriptions = Subscription.query.filter_by(is_active=True).all()
        print(f'\n   活跃订阅数量: {len(active_subscriptions)}')
        
        for subscription in active_subscriptions:
            order = subscription.order
            print(f'     订阅ID: {subscription.id}, 订单: {order.order_id if order else "无"}, 分组ID: {subscription.group_id}')
        
        if not groups or not active_subscriptions:
            print('\n⚠ 系统中没有分组或活跃订阅，无法进行测试')
            return
        
        # 2. 测试批量同步服务
        print(f'\n2. 测试批量同步服务:')
        
        sync_service = SubscriptionSyncService()
        
        # 选择第一个分组进行测试
        test_group = groups[0]
        print(f'   使用测试分组: {test_group.name} (ID: {test_group.id})')
        
        # 测试全面板批量同步
        print(f'\n   开始全面板批量同步...')
        result = sync_service.sync_group_all_panels(test_group.id)
        
        print(f'\n3. 同步结果:')
        print(f'   同步成功: {result["success"]}')
        print(f'   分组名称: {result.get("group_name", "未知")}')
        print(f'   影响订阅数: {result.get("affected_subscriptions", 0)}')
        print(f'   面板数量: {result.get("panel_count", 0)}')
        print(f'   成功数量: {result.get("success_count", 0)}')
        print(f'   失败数量: {result.get("failed_count", 0)}')
        
        if result.get('error'):
            print(f'   错误信息: {result["error"]}')
        
        # 详细面板结果
        panel_results = result.get('panel_results', [])
        if panel_results:
            print(f'\n   面板详细结果:')
            for panel_result in panel_results:
                print(f'     面板: {panel_result["panel_name"]}')
                print(f'       成功: {panel_result["success_count"]}')
                print(f'       失败: {panel_result["failed_count"]}')
                print(f'       状态: {"成功" if panel_result.get("success", False) else "失败"}')
                if panel_result.get('error'):
                    print(f'       错误: {panel_result["error"]}')
                if panel_result.get('message'):
                    print(f'       消息: {panel_result["message"]}')
                print()
        
        # 4. 测试单面板批量同步
        if test_group.panels:
            test_panel = test_group.panels[0]
            print(f'4. 测试单面板批量同步:')
            print(f'   使用测试面板: {test_panel.name}')
            
            result = sync_service.sync_group_subscriptions_for_new_panel(test_group.id, test_panel.id)
            
            print(f'\n   单面板同步结果:')
            print(f'   同步成功: {result["success"]}')
            print(f'   影响订阅数: {result.get("affected_subscriptions", 0)}')
            print(f'   成功数量: {result.get("success_count", 0)}')
            print(f'   失败数量: {result.get("failed_count", 0)}')
            
            if result.get('error'):
                print(f'   错误信息: {result["error"]}')
        
        # 5. 测试通用批量同步
        print(f'\n5. 测试通用批量同步:')
        
        # 获取前3个订阅进行测试
        test_subscription_ids = [sub.id for sub in active_subscriptions[:3]]
        print(f'   测试订阅ID: {test_subscription_ids}')
        
        result = sync_service.sync_subscriptions_batch(
            subscription_ids=test_subscription_ids,
            group_id=test_group.id
        )
        
        print(f'\n   通用批量同步结果:')
        print(f'   总订阅数: {result.get("total_subscriptions", 0)}')
        print(f'   成功数量: {result.get("success_count", 0)}')
        print(f'   失败数量: {result.get("failed_count", 0)}')
        
        if result.get('error'):
            print(f'   错误信息: {result["error"]}')
        
        panel_results = result.get('panel_results', {})
        if panel_results:
            print(f'\n   面板同步详情:')
            for panel_id, panel_result in panel_results.items():
                print(f'     面板: {panel_result["panel_name"]}')
                inbound_results = panel_result.get('inbound_results', {})
                for inbound_id, inbound_result in inbound_results.items():
                    print(f'       入站 {inbound_id}: {"成功" if inbound_result["success"] else "失败"} '
                          f'({inbound_result["client_count"]} 个客户端)')
                    print(f'         消息: {inbound_result["message"]}')
        
        print(f'\n=== 测试完成 ===')

if __name__ == '__main__':
    main()
