<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>响应处理测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container py-4">
        <h1>JavaScript响应处理测试</h1>
        
        <div class="row mt-4">
            <div class="col-12">
                <button type="button" class="btn btn-primary" onclick="testBasicFetch()">基础Fetch测试</button>
                <button type="button" class="btn btn-warning" onclick="testPreviewAPI()">预览API测试</button>
                <button type="button" class="btn btn-danger" onclick="testExecuteFlow()">执行流程测试</button>
                <button type="button" class="btn btn-info" onclick="testLoadingOnly()">仅测试加载状态</button>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">详细日志</div>
                    <div class="card-body">
                        <div id="test-log" style="max-height: 500px; overflow-y: auto; font-family: monospace; font-size: 12px; background: #f8f9fa; padding: 10px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let logCounter = 0;
        
        function addTestLog(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
            const counter = String(++logCounter).padStart(3, '0');
            const color = type === 'error' ? 'red' : type === 'success' ? 'green' : type === 'warning' ? 'orange' : 'blue';
            
            logDiv.innerHTML += `<div style="color: ${color};">[${counter}] [${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function showLoading(message) {
            addTestLog(`🔄 showLoading: ${message}`, 'info');
            let loadingDiv = document.getElementById('loading-indicator');
            if (!loadingDiv) {
                addTestLog('📝 创建新的加载指示器', 'info');
                loadingDiv = document.createElement('div');
                loadingDiv.id = 'loading-indicator';
                loadingDiv.className = 'alert alert-info d-flex align-items-center mt-3';
                loadingDiv.innerHTML = `
                    <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                    <span id="loading-message">${message}</span>
                `;
                document.querySelector('.container').appendChild(loadingDiv);
                addTestLog('✅ 加载指示器已创建并插入DOM', 'success');
            } else {
                addTestLog('📝 更新现有加载指示器', 'info');
                document.getElementById('loading-message').textContent = message;
                loadingDiv.style.display = 'flex';
                addTestLog('✅ 加载指示器已更新', 'success');
            }
        }
        
        function hideLoading() {
            addTestLog('🔄 hideLoading: 开始隐藏加载状态', 'info');
            const loadingDiv = document.getElementById('loading-indicator');
            if (loadingDiv) {
                loadingDiv.style.display = 'none';
                addTestLog('✅ hideLoading: 加载状态已隐藏', 'success');
            } else {
                addTestLog('❌ hideLoading: 找不到加载指示器', 'error');
            }
        }
        
        function testLoadingOnly() {
            addTestLog('🧪 开始测试加载状态管理', 'info');
            showLoading('测试加载状态...');
            
            setTimeout(() => {
                addTestLog('⏰ 3秒后自动隐藏加载状态', 'warning');
                hideLoading();
            }, 3000);
        }
        
        function testBasicFetch() {
            addTestLog('🧪 开始基础Fetch测试', 'info');
            showLoading('正在进行基础Fetch测试...');
            
            addTestLog('📡 发起fetch请求到 /admin/api/traffic-optimization/stats', 'info');
            
            fetch('/admin/api/traffic-optimization/stats')
                .then(response => {
                    addTestLog(`📡 收到响应: status=${response.status}, ok=${response.ok}`, 'info');
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    addTestLog(`📊 解析JSON成功: ${JSON.stringify(data).substring(0, 100)}...`, 'success');
                    addTestLog('🔄 调用hideLoading()', 'info');
                    hideLoading();
                    addTestLog('✅ 基础Fetch测试完成', 'success');
                })
                .catch(error => {
                    addTestLog(`❌ Fetch错误: ${error.message}`, 'error');
                    addTestLog('🔄 在catch中调用hideLoading()', 'info');
                    hideLoading();
                });
        }
        
        function testPreviewAPI() {
            addTestLog('🧪 开始预览API测试', 'info');
            showLoading('正在生成清理预览...');
            
            addTestLog('📡 发起fetch请求到 /admin/api/traffic-optimization/preview', 'info');
            
            fetch('/admin/api/traffic-optimization/preview')
                .then(response => {
                    addTestLog(`📡 预览API响应: status=${response.status}`, 'info');
                    return response.json();
                })
                .then(data => {
                    addTestLog(`📊 预览API数据: success=${data.success}`, 'success');
                    if (data.preview) {
                        addTestLog(`📊 预览详情: will_delete_count=${data.preview.will_delete_count}`, 'info');
                    }
                    addTestLog('🔄 调用hideLoading()', 'info');
                    hideLoading();
                    addTestLog('✅ 预览API测试完成', 'success');
                })
                .catch(error => {
                    addTestLog(`❌ 预览API错误: ${error.message}`, 'error');
                    addTestLog('🔄 在catch中调用hideLoading()', 'info');
                    hideLoading();
                });
        }
        
        function testExecuteFlow() {
            addTestLog('🧪 开始执行流程测试', 'info');
            showLoading('正在检查清理数据...');
            
            addTestLog('📡 模拟executeCleanup流程', 'info');
            
            fetch('/admin/api/traffic-optimization/preview')
                .then(response => {
                    addTestLog(`📡 执行流程响应: status=${response.status}`, 'info');
                    return response.json();
                })
                .then(data => {
                    addTestLog(`📊 执行流程数据: success=${data.success}`, 'success');
                    addTestLog('🔄 调用hideLoading()', 'info');
                    hideLoading();
                    
                    if (data.success && data.preview && data.preview.will_delete_count > 0) {
                        addTestLog(`🎯 发现 ${data.preview.will_delete_count} 条可清理记录`, 'warning');
                    } else if (data.success && data.preview && data.preview.will_delete_count === 0) {
                        addTestLog('🎯 没有需要清理的数据', 'info');
                    } else {
                        addTestLog('❌ 获取清理预览失败', 'error');
                    }
                    addTestLog('✅ 执行流程测试完成', 'success');
                })
                .catch(error => {
                    addTestLog(`❌ 执行流程错误: ${error.message}`, 'error');
                    addTestLog('🔄 在catch中调用hideLoading()', 'info');
                    hideLoading();
                });
        }
        
        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            addTestLog('🚀 页面加载完成，响应处理测试工具就绪', 'success');
            addTestLog('💡 点击按钮测试不同的响应处理场景', 'info');
        });
    </script>
</body>
</html>