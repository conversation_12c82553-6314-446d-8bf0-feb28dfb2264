#!/usr/bin/env python3
"""
简化的孤儿数据风险分析
"""
from app import create_app
from models import db, Subscription, Order, NodeConfig, TrafficStats
from sqlalchemy import text

def simple_orphan_analysis():
    """简化的孤儿数据风险分析"""
    app = create_app()
    with app.app_context():
        print('=== 孤儿数据风险分析 ===\n')
        
        # 1. 当前数据库状态
        print('1. 当前数据库状态:')
        
        total_subscriptions = Subscription.query.count()
        active_subscriptions = Subscription.query.filter_by(is_active=True).count()
        total_orders = Order.query.count()
        total_node_configs = NodeConfig.query.count()
        active_node_configs = NodeConfig.query.filter_by(is_active=True).count()
        total_traffic_stats = TrafficStats.query.count()
        
        print(f'   总订阅数: {total_subscriptions} (活跃: {active_subscriptions})')
        print(f'   总订单数: {total_orders}')
        print(f'   总节点配置: {total_node_configs} (活跃: {active_node_configs})')
        print(f'   总流量统计: {total_traffic_stats}')
        
        # 2. 检查孤儿TrafficStats（最重要的孤儿数据）
        print(f'\n2. 检查孤儿流量统计数据:')
        
        # 获取所有活跃订阅ID
        active_subscription_ids = [sub.id for sub in Subscription.query.filter_by(is_active=True).all()]
        print(f'   活跃订阅ID: {active_subscription_ids}')
        
        # 查找孤儿流量统计
        if active_subscription_ids:
            orphaned_traffic_stats = TrafficStats.query.filter(
                ~TrafficStats.subscription_id.in_(active_subscription_ids)
            ).all()
        else:
            orphaned_traffic_stats = TrafficStats.query.all()
        
        print(f'   孤儿流量统计: {len(orphaned_traffic_stats)} 条')
        
        if orphaned_traffic_stats:
            print(f'   孤儿流量统计详情:')
            for i, stat in enumerate(orphaned_traffic_stats[:5]):  # 只显示前5条
                print(f'     - 统计ID: {stat.id}, 订阅ID: {stat.subscription_id}, 流量: {stat.total_bytes/(1024**2):.2f}MB')
            if len(orphaned_traffic_stats) > 5:
                print(f'     ... 还有 {len(orphaned_traffic_stats)-5} 条')
        
        # 3. 检查孤儿NodeConfig
        print(f'\n3. 检查孤儿节点配置:')
        
        # 使用原生SQL查询避免枚举问题
        panel_addresses = []
        try:
            result = db.session.execute(text("SELECT base_url FROM xui_panel"))
            for row in result:
                base_url = row[0]
                server_address = base_url.replace('http://', '').replace('https://', '').split(':')[0]
                panel_addresses.append(server_address)
        except Exception as e:
            print(f'   查询面板地址失败: {e}')
        
        print(f'   面板服务器地址: {panel_addresses}')
        
        # 查找孤儿节点配置
        orphaned_node_configs = []
        for config in NodeConfig.query.filter_by(is_active=True).all():
            if config.server_address not in panel_addresses:
                orphaned_node_configs.append(config)
        
        print(f'   孤儿节点配置: {len(orphaned_node_configs)} 个')
        
        if orphaned_node_configs:
            print(f'   孤儿节点配置详情:')
            for config in orphaned_node_configs[:3]:  # 只显示前3个
                print(f'     - 配置ID: {config.id}, 服务器: {config.server_address}, 邮箱: {config.client_email}')
        
        # 4. 分析各种删除场景的数据清理情况
        print(f'\n4. 各种删除场景分析:')
        
        print(f'\n   场景1: 订阅管理删除订阅')
        print(f'   实现位置: routes/admin.py delete_subscription')
        print(f'   数据清理: ✅ 完整 (TrafficStats, RenewalTask, NodeConfig, 流量基准)')
        print(f'   孤儿风险: 🟢 低风险')
        
        print(f'\n   场景2: 到期删除订阅')
        print(f'   实现位置: expiration_service._process_single_expired_subscription')
        print(f'   数据清理: ✅ 完整 (调用subscription_service.delete_subscription_and_order)')
        print(f'   孤儿风险: 🟢 低风险')
        
        print(f'\n   场景3: 流量用完删除订阅')
        print(f'   实现位置: expiration_service._process_single_traffic_exhausted_subscription')
        print(f'   数据清理: ✅ 完整 (调用subscription_service.delete_subscription_and_order)')
        print(f'   孤儿风险: 🟢 低风险')
        
        print(f'\n   场景4: 分组管理删除XUI面板')
        print(f'   实现位置: routes/admin.py delete_panel, remove_panel_from_group')
        print(f'   数据清理: ⚠️  部分 (NodeConfig已处理, TrafficStats需要检查)')
        print(f'   孤儿风险: 🟡 中等风险')
        
        # 5. 风险评估
        print(f'\n5. 风险评估:')
        
        total_orphans = len(orphaned_traffic_stats) + len(orphaned_node_configs)
        
        if total_orphans == 0:
            risk_level = '🟢 低风险'
            recommendation = '当前数据状态良好，无需特殊处理'
        elif total_orphans <= 10:
            risk_level = '🟡 中等风险'
            recommendation = '建议定期清理孤儿数据'
        else:
            risk_level = '🔴 高风险'
            recommendation = '建议立即清理孤儿数据并增强删除机制'
        
        print(f'   总孤儿数据: {total_orphans} 项')
        print(f'   风险等级: {risk_level}')
        print(f'   建议: {recommendation}')
        
        # 6. 具体建议
        print(f'\n6. 具体建议:')
        
        print(f'\n   💡 改进建议:')
        print(f'   1. 增强面板删除时的数据清理')
        print(f'      - 在delete_panel中增加TrafficStats清理')
        print(f'      - 在remove_panel_from_group中处理流量基准数据')
        
        print(f'\n   2. 定期孤儿数据清理')
        print(f'      - 使用现有的data_cleanup_service定期清理')
        print(f'      - 在管理员界面提供清理工具')
        
        print(f'\n   3. 统一删除接口')
        print(f'      - 确保所有删除操作都调用完整的清理服务')
        print(f'      - 添加事务安全保障')
        
        # 7. 回答用户问题
        print(f'\n7. 回答用户问题:')
        print(f'   问题: 各种删除操作是否会留下孤儿数据？')
        print(f'   ')
        print(f'   📋 答案:')
        print(f'   ✅ 订阅相关删除 (管理员删除、到期删除、流量用完删除):')
        print(f'      - 不会留下孤儿数据')
        print(f'      - 数据清理机制完整')
        print(f'      - 包含TrafficStats、NodeConfig、流量基准等所有相关数据')
        print(f'   ')
        print(f'   ⚠️  面板相关删除 (分组管理删除XUI面板):')
        print(f'      - 可能留下少量TrafficStats孤儿数据')
        print(f'      - NodeConfig已有自动清理机制')
        print(f'      - 建议增强流量数据清理')
        print(f'   ')
        print(f'   🎯 总体评估: 大部分场景数据清理完整，面板删除需要小幅改进')
        
        print(f'\n=== 孤儿数据风险分析完成 ===')

if __name__ == '__main__':
    simple_orphan_analysis()
