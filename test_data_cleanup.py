#!/usr/bin/env python3
"""
测试数据清理功能
"""
from app import create_app
from models import db, XUIPanel, NodeConfig, TrafficStats, Subscription, Order
from services.data_cleanup_service import data_cleanup_service
import uuid
from datetime import datetime

def test_data_cleanup():
    """测试数据清理功能"""
    app = create_app()
    with app.app_context():
        print('=== 数据清理功能测试 ===\n')
        
        # 1. 生成清理报告
        print('1. 生成数据清理报告:')
        report_result = data_cleanup_service.get_cleanup_report()
        
        if report_result['success']:
            report = report_result['report']
            print(f'   节点配置统计:')
            print(f'     总数: {report["node_configs"]["total"]}')
            print(f'     活跃: {report["node_configs"]["active"]}')
            print(f'     非活跃: {report["node_configs"]["inactive"]}')
            print(f'     潜在孤儿: {report["node_configs"]["potential_orphaned"]}')
            
            print(f'\n   流量统计:')
            print(f'     总记录数: {report["traffic_stats"]["total"]}')
            print(f'     潜在孤儿: {report["traffic_stats"]["potential_orphaned"]}')
            
            print(f'\n   面板统计:')
            print(f'     总数: {report["panels"]["total"]}')
            print(f'     活跃: {report["panels"]["active"]}')
            
            print(f'\n   订阅统计:')
            print(f'     活跃: {report["subscriptions"]["active"]}')
            
            print(f'\n   清理建议:')
            for recommendation in report_result['recommendations']:
                print(f'     - {recommendation}')
        else:
            print(f'   ❌ 生成报告失败: {report_result["error"]}')
        
        # 2. 测试孤儿节点配置清理
        print(f'\n2. 测试孤儿节点配置清理:')
        
        # 创建一个测试的孤儿节点配置
        test_config = NodeConfig(
            order_id=1,  # 假设存在的订单ID
            server_address='test.orphaned.server',  # 不存在的服务器地址
            server_port=443,
            client_email=f'test_orphan_{uuid.uuid4().hex[:8]}@example.com',
            client_id=str(uuid.uuid4()),
            vless_config='vless://test-orphan-config',
            is_active=True
        )
        
        db.session.add(test_config)
        db.session.commit()
        print(f'   创建测试孤儿节点配置: {test_config.id}')
        
        # 执行清理
        cleanup_result = data_cleanup_service.cleanup_orphaned_node_configs()
        if cleanup_result['success']:
            print(f'   ✓ 清理成功: {cleanup_result["message"]}')
            print(f'   清理数量: {cleanup_result["cleaned_count"]}')
            
            if cleanup_result.get('details'):
                print(f'   清理详情:')
                for detail in cleanup_result['details'][:3]:  # 只显示前3个
                    print(f'     - 配置ID: {detail["config_id"]}, 服务器: {detail["server_address"]}')
        else:
            print(f'   ❌ 清理失败: {cleanup_result["error"]}')
        
        # 3. 测试孤儿流量统计清理
        print(f'\n3. 测试孤儿流量统计清理:')
        
        # 创建一个测试的孤儿流量统计
        test_stat = TrafficStats(
            subscription_id=99999,  # 不存在的订阅ID
            user_id=1,  # 假设存在的用户ID
            group_id=1,  # 假设存在的分组ID
            upload_bytes=1024*1024,  # 1MB
            download_bytes=2*1024*1024,  # 2MB
            total_bytes=3*1024*1024,  # 3MB
            recorded_at=datetime.utcnow(),
            created_at=datetime.utcnow()
        )
        
        db.session.add(test_stat)
        db.session.commit()
        print(f'   创建测试孤儿流量统计: {test_stat.id}')
        
        # 执行清理
        cleanup_result = data_cleanup_service.cleanup_orphaned_traffic_stats()
        if cleanup_result['success']:
            print(f'   ✓ 清理成功: {cleanup_result["message"]}')
            print(f'   清理数量: {cleanup_result["cleaned_count"]}')
            print(f'   释放流量: {cleanup_result.get("total_traffic_mb_cleaned", 0)} MB')
        else:
            print(f'   ❌ 清理失败: {cleanup_result["error"]}')
        
        # 4. 测试综合清理
        print(f'\n4. 测试综合清理:')
        
        # 再创建一些测试数据
        test_config2 = NodeConfig(
            order_id=1,
            server_address='another.orphaned.server',
            server_port=443,
            client_email=f'test_orphan2_{uuid.uuid4().hex[:8]}@example.com',
            client_id=str(uuid.uuid4()),
            vless_config='vless://test-orphan-config-2',
            is_active=True
        )
        
        test_stat2 = TrafficStats(
            subscription_id=88888,  # 不存在的订阅ID
            user_id=1,
            group_id=1,
            upload_bytes=5*1024*1024,  # 5MB
            download_bytes=10*1024*1024,  # 10MB
            total_bytes=15*1024*1024,  # 15MB
            recorded_at=datetime.utcnow(),
            created_at=datetime.utcnow()
        )
        
        db.session.add(test_config2)
        db.session.add(test_stat2)
        db.session.commit()
        print(f'   创建额外测试数据: 节点配置 {test_config2.id}, 流量统计 {test_stat2.id}')
        
        # 执行综合清理
        comprehensive_result = data_cleanup_service.comprehensive_cleanup()
        if comprehensive_result['success']:
            print(f'   ✓ 综合清理成功: {comprehensive_result["message"]}')
            
            results = comprehensive_result['results']
            print(f'   清理结果汇总:')
            print(f'     总清理项目: {results["total_cleaned_items"]}')
            print(f'     总释放流量: {results["total_traffic_mb_cleaned"]} MB')
            
            # 详细结果
            if results['orphaned_node_configs']['success']:
                print(f'     孤儿节点配置: {results["orphaned_node_configs"]["cleaned_count"]} 个')
            
            if results['orphaned_traffic_stats']['success']:
                print(f'     孤儿流量统计: {results["orphaned_traffic_stats"]["cleaned_count"]} 条')
        else:
            print(f'   ❌ 综合清理失败: {comprehensive_result["error"]}')
        
        # 5. 验证清理效果
        print(f'\n5. 验证清理效果:')
        
        # 检查测试数据是否被正确处理
        remaining_test_configs = NodeConfig.query.filter(
            NodeConfig.server_address.in_(['test.orphaned.server', 'another.orphaned.server']),
            NodeConfig.is_active == True
        ).count()
        
        remaining_test_stats = TrafficStats.query.filter(
            TrafficStats.subscription_id.in_([99999, 88888])
        ).count()
        
        print(f'   剩余测试节点配置（活跃）: {remaining_test_configs}')
        print(f'   剩余测试流量统计: {remaining_test_stats}')
        
        if remaining_test_configs == 0:
            print('   ✓ 孤儿节点配置清理成功')
        else:
            print('   ⚠ 孤儿节点配置清理可能不完整')
        
        if remaining_test_stats == 0:
            print('   ✓ 孤儿流量统计清理成功')
        else:
            print('   ⚠ 孤儿流量统计清理可能不完整')
        
        # 6. 最终报告
        print(f'\n6. 最终数据清理报告:')
        final_report = data_cleanup_service.get_cleanup_report()
        
        if final_report['success']:
            final_data = final_report['report']
            print(f'   当前数据状态:')
            print(f'     活跃节点配置: {final_data["node_configs"]["active"]}')
            print(f'     潜在孤儿节点配置: {final_data["node_configs"]["potential_orphaned"]}')
            print(f'     潜在孤儿流量统计: {final_data["traffic_stats"]["potential_orphaned"]}')
            
            print(f'\n   当前建议:')
            for recommendation in final_report['recommendations']:
                print(f'     - {recommendation}')
        
        print(f'\n=== 数据清理功能测试完成 ===')

if __name__ == '__main__':
    test_data_cleanup()
